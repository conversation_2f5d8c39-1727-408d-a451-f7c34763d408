import React, { useState } from "react";
import { useGetApiQuery } from "../store/api/commonApi";

const CommonTable = ({
  url,
  searchParams = {},
  showPagination = false,
  showSearch = false,
  rowsPerPageOptions = [10, 20, 30, 40, 50],
  onRowAction = () => {},
  actions = [],
  // actions = ["view", "edit", "delete"],
}) => {
  const [search, setSearch] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [params, setParams] = useState(searchParams);

  const { data, isLoading, isError, isFetching } = useGetApiQuery({
    url,
    params: {
      ...params,
      _page: currentPage,
      _limit: rowsPerPage,
      q: search,
    },
  });

  const handleSearchChange = (e) => {
    setSearch(e.target.value);
    setCurrentPage(1); // Reset page on search change
  };

  const handlePageChange = (pageNumber) => {
    setCurrentPage(pageNumber);
  };

  const handleRowsPerPageChange = (e) => {
    setRowsPerPage(Number(e.target.value));
    setCurrentPage(1); // Reset page on rows per page change
  };

  const totalPages = data?.totalPages || 1;

  // Skeleton Loader Component
  const SkeletonLoader = () => (
    <div className="bg-white animate-pulse">
      <div className="flex flex-col space-y-4">
        {Array.from({ length: 5 }).map((_, index) => (
          <div
            key={index}
            className="flex space-x-4 p-4 border-b border-gray-200"
          >
            <div className="w-10 h-6 bg-gray-300 rounded"></div>
            <div className="flex-1 h-6 bg-gray-300 rounded"></div>
            <div className="w-24 h-6 bg-gray-300 rounded"></div>
            <div className="w-32 h-6 bg-gray-300 rounded"></div>
          </div>
        ))}
      </div>
    </div>
  );

  return (
    <div className="table-container">
      <div className="flex justify-between items-center mb-4">
        {showSearch && (
          <input
            type="text"
            placeholder="Search..."
            value={search}
            onChange={handleSearchChange}
            className="px-3 py-2 border rounded-lg text-sm"
          />
        )}
        {showPagination && (
          <div className="flex items-center space-x-4">
            <select
              value={rowsPerPage}
              onChange={handleRowsPerPageChange}
              className="px-3 py-2 border rounded-lg text-sm"
            >
              {rowsPerPageOptions.map((option) => (
                <option key={option} value={option}>
                  {option} Rows per page
                </option>
              ))}
            </select>
            <div className="flex space-x-2">
              {Array.from({ length: totalPages }, (_, i) => i + 1).map(
                (page) => (
                  <button
                    key={page}
                    onClick={() => handlePageChange(page)}
                    className={`px-3 py-2 rounded-lg ${
                      page === currentPage
                        ? "bg-primary-500 text-white"
                        : "bg-gray-200"
                    }`}
                  >
                    {page}
                  </button>
                )
              )}
            </div>
          </div>
        )}
      </div>

      <div className="overflow-x-auto rounded-lg shadow-md border border-gray-100">
        <table className="min-w-full">
          <thead className="bg-gray-100">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                #
              </th>
              {/* You can dynamically map columns here based on your data */}
              {data?.columns?.map((column, index) => (
                <th
                  key={index}
                  className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
                >
                  {column.title}
                </th>
              ))}
              {actions.length > 0 && (
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                  Actions
                </th>
              )}
            </tr>
          </thead>
          <tbody className="bg-white">
            {isLoading ? (
              <SkeletonLoader />
            ) : data?.items?.length > 0 ? (
              data?.items?.map((item, index) => (
                <tr
                  key={item.id}
                  className={index % 2 === 0 ? "bg-light-green-bg" : "bg-white"}
                >
                  <td className="px-6 py-4 text-sm font-medium text-gray-900">
                    {item.id}
                  </td>
                  {data?.columns?.map((column, colIndex) => (
                    <td
                      key={colIndex}
                      className="px-6 py-4 text-sm text-gray-700"
                    >
                      {item[column.field]}
                    </td>
                  ))}
                  {actions.length > 0 && (
                    <td className="px-6 py-4 text-sm">
                      {actions.includes("view") && (
                        <button onClick={() => onRowAction("view", item)}>
                          View
                        </button>
                      )}
                      {actions.includes("edit") && (
                        <button onClick={() => onRowAction("edit", item)}>
                          Edit
                        </button>
                      )}
                      {actions.includes("delete") && (
                        <button onClick={() => onRowAction("delete", item)}>
                          Delete
                        </button>
                      )}
                    </td>
                  )}
                </tr>
              ))
            ) : (
              <tr>
                <td
                  colSpan={
                    actions.length > 0
                      ? data?.columns?.length + 1
                      : data?.columns?.length
                  }
                  className="px-6 py-4 text-sm text-center text-gray-700"
                >
                  <div className="py-8 text-xl text-gray-500">
                    No Data Found
                  </div>
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default CommonTable;
