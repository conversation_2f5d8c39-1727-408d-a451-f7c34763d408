import React from "react";
import BangladeshMap from "../../assets/bd_map.png";
import { Icon } from "@iconify/react";

const StatCard = ({
  value = "##",
  title,
  icon,
  bgColor = "bg-[#F7FFFF]",
  iconBgColor = "bg-white",
  iconColor = "text-blue-600",
}) => {
  return (
    <div
      className={`p-4 rounded-[12px] shadow-[0px_2px_4px_rgba(0,0,0,0.1)] border border-[#E8E8E8] ${bgColor} h-[160px] flex flex-col justify-between`}
    >
      <div className="flex items-center justify-between">
        <div className="text-5xl font-bold text-green-600">{value}</div>
        <div
          className={`w-12 h-12 ${iconBgColor} rounded-[12px] flex items-center justify-center shadow-[0_2px_6px_rgba(0,0,0,0.15)]`}
        >
          <Icon icon={icon} className={`w-6 h-6 ${iconColor}`} />
        </div>
      </div>
      <h3 className="font-semibold text-gray-700 text-base">{title}</h3>
    </div>
  );
};

const Map = () => {
  return (
    <section className="mt-3">
      <div className="max-w-7xl mx-auto px-4 py-12">
        <div className="flex flex-col lg:flex-row gap-6 items-stretch">
          {/* Map */}
          <div className="w-full lg:max-w-[48%] bg-green-50 p-4 shadow-md rounded-lg flex items-center justify-center overflow-hidden">
            <div className="w-full h-full flex flex-col items-center justify-center max-h-[500px]">
              {/* <img
                src={BangladeshMap}
                alt="Bangladesh Choropleth Map"
                className="w-full h-full object-contain rounded-lg"
              /> */}
              <iframe
                width="600"
                height="400"
                seamless
                frameBorder="0"
                scrolling="no"
                src="http://18.212.44.37:8088/superset/explore/p/OBbKL2kpZ7N/?standalone=1&height=400"
              ></iframe>
            </div>
          </div>

          {/* Cards */}
          <div className="w-full lg:max-w-[52%] bg-green-50 p-4 shadow-md rounded-lg">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <StatCard
                title="Total Climate Projects"
                icon="mdi:home"
                iconColor="text-blue-600"
              />
              <StatCard
                title="Climate Projects With Only Domestic Finance"
                icon="mdi:office-building"
                iconColor="text-purple-600"
              />
              <StatCard
                title="Climate Projects With Only External Finance"
                icon="mdi:earth"
                iconColor="text-red-600"
              />
              <StatCard
                title="Climate Projects With Both Domestic And External Finance"
                icon="mdi:currency-usd"
                iconColor="text-yellow-600"
              />
              <StatCard
                title="Projects Related To NAP With Total Funding"
                icon="mdi:file-document-outline"
                iconColor="text-red-600"
              />
              <StatCard
                title="Projects Related To NDC With Total Funding"
                icon="mdi:target"
                iconColor="text-yellow-600"
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Map;
