import React, { useState } from 'react';
import { Icon } from '@iconify/react';
import FeedbackForm from './FeedbackForm';

const Contact = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    feedback: ''
  });

  const [mapSrc, setMapSrc] = useState("https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d14684160.950149236!2d82.79284408593751!3d23.857643139374517!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x30adaaed80e18ba7%3A0xf2d28e0c4e1fc6b!2sBangladesh!5e0!3m2!1sen!2sbd!4v1734363123456!5m2!1sen!2sbd");

  const addresses = [
    "123, abc, street, Dhaka -10000",
    "123, abc, street, Dhaka -10000"
  ];

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = () => {
    console.log('Form submitted:', formData);
    alert('Thank you for your feedback!');
    // Reset form
    setFormData({
      name: '',
      email: '',
      subject: '',
      feedback: ''
    });
  };

  const searchAddressOnMap = (address) => {
    // Encode the address for URL
    const encodedAddress = encodeURIComponent(address);
    // Create new Google Maps embed URL with the specific address
    const newMapSrc = `https://www.google.com/maps/embed/v1/place?key=AIzaSyBFw0Qbyq9zTFTd-tUY6dS0e1-PGAEu3J0&q=${encodedAddress}`;
    
    // For demonstration, we'll use a search URL format that works without API key
    const searchMapSrc = `https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3651.9014999319805!2d90.39743931498169!3d23.750895194619546!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3755b8b087026b81%3A0x8fa563bbdd5904c2!2sDhaka%2C%20Bangladesh!5e0!3m2!1sen!2sbd&q=${encodedAddress}`;
    
    setMapSrc(searchMapSrc);
  };

  const handleAddressIconClick = () => {
    // Search for the first address when icon is clicked
    if (addresses.length > 0) {
      searchAddressOnMap(addresses[0]);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      

      <div className="max-w-7xl mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 bg-green-50 p-4 rounded-lg shadow-md">
          {/* Left Column - Contact Info and Map */}
          <div className="space-y-6">
            {/* Phone & Email Section */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h2 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                Phone & email
              </h2>
              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <Icon icon="mdi:phone" className="w-5 h-5 text-blue-500" />
                  <span className="text-gray-700">+880##########</span>
                </div>
                <div className="flex items-center space-x-3">
                  <Icon icon="mdi:email" className="w-5 h-5 text-blue-500" />
                  <span className="text-gray-700"><EMAIL></span>
                </div>
              </div>
            </div>

            {/* Address Section */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-semibold text-gray-800">Address</h2>
                <Icon 
                  icon="mdi:map-marker" 
                  className="w-5 h-5 text-blue-500 cursor-pointer hover:text-blue-700 transition-colors duration-200" 
                  onClick={handleAddressIconClick}
                  title="Search address on map"
                />
              </div>
              <div className="space-y-3">
                {addresses.map((address, index) => (
                  <div key={index} className="flex items-start space-x-2">
                    <Icon 
                      icon="mdi:map-marker-outline" 
                      className="w-4 h-4 text-blue-400 mt-1 cursor-pointer hover:text-blue-600 transition-colors duration-200" 
                      onClick={() => searchAddressOnMap(address)}
                      title="Search this address on map"
                    />
                    <div onClick={() => searchAddressOnMap(address)} className="cursor-pointer hover:text-blue-600 transition-colors duration-200">
                      <div className="text-gray-700">{address.split(',')[0]}, {address.split(',')[1]}, {address.split(',')[2]},</div>
                      <div className="text-gray-700">{address.split(',')[3]}</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Right Column - Bangladesh Map */}
          <div className="bg-white rounded-lg shadow-sm p-4">
            <div className="w-full h-80 rounded-lg overflow-hidden relative">
              <iframe
                src={mapSrc}
                width="100%"
                height="100%"
                style={{ border: 0 }}
                loading="lazy"
                referrerPolicy="no-referrer-when-downgrade"
                title="Bangladesh Map"
                className="rounded-lg"
              ></iframe>
              <div 
                className="absolute inset-0 cursor-pointer"
                onClick={() => window.open(`https://www.google.com/maps/search/${encodeURIComponent(addresses[0])}`, '_blank')}
                title="Click to open in Google Maps"
              ></div>
              <div className="absolute bottom-2 right-2 bg-white px-2 py-1 rounded text-xs text-gray-600 shadow-sm pointer-events-none">
                Bangladesh
              </div>
            </div>
          </div>
        </div>
        

        <section className="py-16 lg:py-20 bg-gray-50">
          <div className="max-w-container mx-auto px-4">
            <FeedbackForm />
          </div>
        </section>
      </div>
    </div>
  );
};

export default Contact;