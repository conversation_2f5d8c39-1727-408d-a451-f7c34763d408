/** @type {import('tailwindcss').Config} */
export default {
  content: ["./index.html", "./src/**/*.{js,ts,jsx,tsx}"],
  theme: {
    extend: {
      fontFamily: {
        archivo: ["Archivo", "sans-serif"],
        sans: ["Archivo", "sans-serif"],
      },
      fontSize: {
        header: ["36px", { lineHeight: "1.2", fontWeight: "600" }],
        "secondary-header": ["20px", { lineHeight: "1.3", fontWeight: "500" }],
        body: ["16px", { lineHeight: "1.5", fontWeight: "400" }],
      },
      colors: {
        primary: {
          50: "#e6f7e6",
          100: "#ccf0cc",
          200: "#99e199",
          300: "#66d266",
          400: "#33c333",
          500: "#00b400",
          600: "#009100",
          700: "#006e00",
          800: "#004b00",
          900: "#002800",
          950: "#001400",
        },
        "nav-bg": "#DAF3DF",
        "light-green-bg": "#F3FFF5",
        "breadcrumb-bg": "#EDFFEA",
      },
      screens: {
        sm: "640px",
        md: "768px",
        lg: "1280px",
        xl: "1440px",
        "2xl": "1600px",
      },
      maxWidth: {
        container: "1280px",
      },
    },
  },
  plugins: [],
};
