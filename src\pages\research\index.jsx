import { Text } from "../../components";
import PageHeader from "../../components/PageHeader";
import Research from "../home/<USER>";

const ResearchPage = () => {
  
  const breadcrumbs = [
    { label: "Home", path: "/" },
    { label: "Research and Publications" }, 
  ];

  return (
    <div className="min-h-screen bg-white">

       {/* Mobile Header - Only shows on mobile */}
      <div className="block md:hidden px-3 py-4 bg-breadcrumb-bg">
        {/* Mobile Breadcrumbs */}
        <nav className="mb-3">
          <div className="flex items-center space-x-1 text-xs text-gray-600">
            <a href="/" className="hover:text-gray-800 transition-colors">Home</a>
            <span>/</span>
            <span className="text-gray-800 font-medium">Research and Innovation</span>
          </div>
        </nav>
        
        {/* Mobile Title */}
        <div className="mb-4">
          <h1 className="text-lg font-bold text-gray-900 leading-tight">
            Research and Innovation
          </h1>
        </div>
        
        {/* Mobile Contact Button */}
        {/* <button 
          onClick={() => console.log("Contact board clicked")}
          className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg font-medium text-sm transition-colors duration-200"
        >
          Contact Board
        </button> */}
      </div>

      {/* Desktop Header - Only shows on desktop */}
      <div className="hidden md:block [&_nav_a]:mx-1 sm:[&_nav_a]:mx-2 md:[&_nav_a]:mx-3 [&_nav_span]:mx-1 sm:[&_nav_span]:mx-2 md:[&_nav_span]:mx-3 [&_nav>*:first-child]:ml-0">
        <div>
          <PageHeader 
           title="Research and Innovation"
            breadcrumbs={breadcrumbs}
            buttonText="Contact Board"
            buttonOnClick={() => console.log("Contact board clicked")}
          />
        </div>
      </div>
     

      {/* Research Section */}
      <section className="py-16 lg:py-20 bg-white">
        <div className="max-w-container mx-auto px-4">
          <Research />
        </div>
      </section>
    </div>
  );
};

export default ResearchPage;