import React from 'react';

const Text = ({ 
  variant = 'body', 
  weight = 'normal',
  className = '', 
  children, 
  as: Component = 'span',
  ...props 
}) => {
  // Define variant classes
  const variantClasses = {
    header: 'text-header',
    'secondary-header': 'text-secondary-header', 
    body: 'text-body'
  };

  // Define weight classes
  const weightClasses = {
    light: variant === 'header' ? 'text-header-light' : 
           variant === 'secondary-header' ? 'text-secondary-header-light' : 
           'text-body-light',
    normal: variantClasses[variant],
    medium: variant === 'body' ? 'text-body-medium' : variantClasses[variant],
    bold: variant === 'header' ? 'text-header-bold' : 
          variant === 'secondary-header' ? 'text-secondary-header-bold' : 
          'text-body-bold'
  };

  const baseClass = weightClasses[weight] || variantClasses[variant];
  const combinedClassName = `${baseClass} ${className}`.trim();

  return (
    <Component className={combinedClassName} {...props}>
      {children}
    </Component>
  );
};

export default Text;
