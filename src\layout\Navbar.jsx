import { Link, useLocation } from "react-router-dom";
import { useState } from "react";
import { Button, Text } from "../components";
import { useAuth } from "../hooks/useAuth";
import img from "../assets/map.png";
import { useSelector } from "react-redux";

const Navbar = () => {
  const location = useLocation();
  const [isWorkingGroupsOpen, setIsWorkingGroupsOpen] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isMobileUserMenuOpen, setIsMobileUserMenuOpen] = useState(false);
  const [mobileDropdownOpen, setMobileDropdownOpen] = useState({});
  const { user, isAuthenticated, isAdmin, logout } = useAuth();
  const reduxUser = useSelector((state) => state.auth?.user);

  const isActive = (path) => {
    if (path === "/") {
      return location.pathname === "/";
    }
    return location.pathname.startsWith(path);
  };

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
    if (isMobileMenuOpen) {
      setMobileDropdownOpen({});
    }
  };

  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false);
    setMobileDropdownOpen({});
  };

  const toggleMobileUserMenu = () => {
    setIsMobileUserMenuOpen(!isMobileUserMenuOpen);
  };

  const closeMobileUserMenu = () => {
    setIsMobileUserMenuOpen(false);
  };

  const toggleMobileDropdown = (itemName) => {
    setMobileDropdownOpen((prev) => ({
      ...prev,
      [itemName]: !prev[itemName],
    }));
  };

  const navItems = [
    {
      path: reduxUser ? "/dashboard" : "/",
      name: reduxUser ? "Dashboard" : "Home",
    },
    {
      path: "/about",
      name: "About BCDP",
    },
    {
      path: "/board",
      name: "BCDP Board",
    },
    {
      path: "/working-groups",
      name: "Working Groups",
      hasDropdown: true,
      dropdownItems: [
        { name: "Climate Finance", path: "/working-groups/climate-finance" },
        { name: "Adaptation", path: "/working-groups/adaptation" },
        { name: "Mitigation", path: "/working-groups/mitigation" },
        { name: "Technology Transfer", path: "/working-groups/technology" },
      ],
    },
    {
      path: "/research",
      name: "Research And Innovation",
    },
    {
      path: "/publications",
      name: "Publications",
    },
    {
      path: "/policies",
      name: "Policies & Laws",
    },
    {
      path: "/contact",
      name: "Contact Us",
    },
  ];

  return (
    <nav className="shadow-lg">
      {/* Top bar with logo and login - Responsive fixed height */}
      <div className="bg-primary-700 h-16 sm:h-18 md:h-[70px] px-3 sm:px-4 relative">
        <div className="max-w-container mx-auto flex items-center justify-between h-full">
          {/* Logo and Title */}
          <div className="flex items-center space-x-2 sm:space-x-4 flex-1 min-w-0">
            <div className="flex-shrink-0">
              <img
                src={img}
                alt="logo"
                className="w-7 h-7 sm:w-9 sm:h-9 md:w-10 md:h-10"
              />
            </div>
            <Text
              variant="body"
              weight="medium"
              as="h1"
              className="text-white text-xs sm:text-base md:text-xl font-semibold leading-tight truncate"
            >
              Bangladesh Climate Development Partnership
            </Text>
          </div>

          {/* Login/Logout Button */}
          {isAuthenticated ? (
            <div className="relative flex-shrink-0">
              {/* Desktop View */}
              <div className="hidden sm:flex items-center space-x-3">
                <span className="text-white text-sm font-medium">
                  Welcome, {user?.name || user?.email || "User"}
                </span>
                {isAdmin && (
                  <Link to="/admin">
                    <Button
                      variant="secondary"
                      size="sm"
                      className="text-sm px-4 py-2 font-medium"
                    >
                      Admin
                    </Button>
                  </Link>
                )}
                <Button
                  variant="secondary"
                  size="sm"
                  className="text-sm px-6 py-2 font-medium"
                  onClick={logout}
                >
                  Logout
                </Button>
              </div>

              {/* Mobile View - Clickable User Area */}
              <div className="sm:hidden">
                <button
                  onClick={toggleMobileUserMenu}
                  className="flex items-center space-x-1 text-white p-2 rounded-md hover:bg-primary-600 transition-colors"
                  aria-label="User menu"
                >
                  <div className="w-6 h-6 bg-white rounded-full flex items-center justify-center">
                    <span className="text-primary-700 text-xs font-semibold">
                      {(user?.name || user?.email || "U")
                        .charAt(0)
                        .toUpperCase()}
                    </span>
                  </div>
                  <svg
                    className={`w-4 h-4 transition-transform duration-200 ${
                      isMobileUserMenuOpen ? "rotate-180" : ""
                    }`}
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M19 9l-7 7-7-7"
                    />
                  </svg>
                </button>

                {/* Mobile User Dropdown */}
                {isMobileUserMenuOpen && (
                  <div className="absolute top-full right-0 mt-2 w-64 bg-white rounded-lg shadow-xl border border-gray-200 z-50">
                    <div className="p-4 border-b border-gray-100">
                      <p className="text-sm text-gray-600 mb-1">
                        Logged in as:
                      </p>
                      <p className="text-sm font-medium text-gray-900 break-all">
                        {user?.name || user?.email || "User"}
                      </p>
                    </div>
                    <div className="p-2 space-y-1">
                      {isAdmin && (
                        <Link
                          to="/admin"
                          onClick={closeMobileUserMenu}
                          className="flex items-center w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md transition-colors"
                        >
                          <svg
                            className="w-4 h-4 mr-2"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
                            />
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                            />
                          </svg>
                          Admin Panel
                        </Link>
                      )}
                      <button
                        onClick={() => {
                          logout();
                          closeMobileUserMenu();
                        }}
                        className="flex items-center w-full px-3 py-2 text-sm text-red-700 hover:bg-red-50 rounded-md transition-colors"
                      >
                        <svg
                          className="w-4 h-4 mr-2"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"
                          />
                        </svg>
                        Logout
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          ) : (
            <Link to="/login" className="flex-shrink-0">
              <Button
                variant="secondary"
                size="sm"
                className="text-xs sm:text-sm px-4 sm:px-8 py-1.5 sm:py-2 font-medium"
              >
                Login
              </Button>
            </Link>
          )}
        </div>

        {/* Overlay to close mobile user menu */}
        {isMobileUserMenuOpen && (
          <div
            className="fixed inset-0 z-30 sm:hidden"
            onClick={closeMobileUserMenu}
          ></div>
        )}
      </div>

      {/* Navigation Menu with custom background - Responsive fixed height */}
      <div
        className="relative border-t border-gray-200 border-b border-primary-100 h-12 sm:h-14 md:h-[80px]"
        style={{ backgroundColor: "#FAFFF3" }}
      >
        <div className="max-w-container mx-auto px-4 h-full">
          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center justify-between h-full w-full">
            {navItems.map((item) => (
              <div key={item.name} className="relative">
                {item.hasDropdown ? (
                  <div
                    className="relative"
                    onClick={() => toggleMobileDropdown(item.name)}
                  >
                    <Link
                      to={item.path}
                      className={`relative text-gray-700 hover:text-primary-700 font-medium transition-all duration-300 flex items-center h-full text-xs sm:text-sm md:text-base ${
                        isActive(item.path)
                          ? "text-primary-700 after:absolute after:bottom-0 after:left-0 after:right-0 after:h-0.5 after:bg-primary-700 after:transition-all after:duration-300"
                          : "after:absolute after:bottom-0 after:left-0 after:right-0 after:h-0.5 after:bg-transparent hover:after:bg-primary-700 after:transition-all after:duration-300"
                      }`}
                    >
                      {item.name}
                      <svg
                        className="ml-1 h-4 w-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M19 9l-7 7-7-7"
                        />
                      </svg>
                    </Link>

                    {/* Dropdown Menu */}
                    {mobileDropdownOpen[item.name] && (
                      <div className="absolute top-full left-0 mt-1 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-50">
                        <div className="py-1">
                          {item.dropdownItems.map((dropdownItem) => (
                            <Link
                              key={dropdownItem.name}
                              to={dropdownItem.path}
                              className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-primary-700 transition-colors"
                            >
                              {dropdownItem.name}
                            </Link>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  <Link
                    to={item.path}
                    className={`relative text-gray-700 hover:text-primary-700 font-medium transition-all duration-300 flex items-center h-full text-xs sm:text-sm md:text-base ${
                      isActive(item.path)
                        ? "text-primary-700 after:absolute after:bottom-0 after:left-0 after:right-0 after:h-0.5 after:bg-primary-700 after:transition-all after:duration-300"
                        : "after:absolute after:bottom-0 after:left-0 after:right-0 after:h-0.5 after:bg-transparent hover:after:bg-primary-700 after:transition-all after:duration-300"
                    }`}
                  >
                    {item.name}
                  </Link>
                )}
              </div>
            ))}
          </div>

          {/* Mobile Navigation */}
          <div className="md:hidden flex items-center justify-between h-full w-full">
            <div className="flex items-center">
              <span className="text-gray-700 font-medium text-sm">Menu</span>
            </div>

            {/* Mobile Hamburger Button */}
            <button
              onClick={toggleMobileMenu}
              className="flex flex-col items-center justify-center w-6 h-6 space-y-0.5 focus:outline-none z-50 relative"
              aria-label="Toggle mobile menu"
            >
              <span
                className={`block w-5 h-0.5 bg-gray-700 transition-all duration-300 ${
                  isMobileMenuOpen ? "rotate-45 translate-y-1" : ""
                }`}
              ></span>
              <span
                className={`block w-5 h-0.5 bg-gray-700 transition-all duration-300 ${
                  isMobileMenuOpen ? "opacity-0" : ""
                }`}
              ></span>
              <span
                className={`block w-5 h-0.5 bg-gray-700 transition-all duration-300 ${
                  isMobileMenuOpen ? "-rotate-45 -translate-y-1" : ""
                }`}
              ></span>
            </button>
          </div>
        </div>

        {/* Mobile Menu Dropdown */}
        <div
          className={`md:hidden absolute top-full left-0 right-0 bg-white border-t border-gray-200 shadow-xl transition-all duration-300 ease-in-out z-40 ${
            isMobileMenuOpen
              ? "opacity-100 transform translate-y-0 visible"
              : "opacity-0 transform -translate-y-2 invisible"
          }`}
        >
          <div className="px-4 py-3 space-y-1 max-h-80 overflow-y-auto">
            {navItems.map((item) => (
              <div key={item.name}>
                {item.hasDropdown ? (
                  <div>
                    {/* Main menu item with click to toggle dropdown */}
                    <div className="flex items-center justify-between">
                      <Link
                        to={item.path}
                        onClick={closeMobileMenu}
                        className={`flex-1 px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                          isActive(item.path)
                            ? "text-primary-700 bg-primary-50 border-l-4 border-primary-700"
                            : "text-gray-700 hover:text-primary-700 hover:bg-gray-50"
                        }`}
                      >
                        {item.name}
                      </Link>
                      {/* Toggle button for dropdown */}
                      <button
                        onClick={() => toggleMobileDropdown(item.name)}
                        className="p-2 text-gray-500 hover:text-primary-700 transition-colors"
                        aria-label={`Toggle ${item.name} submenu`}
                      >
                        <svg
                          className={`w-4 h-4 transition-transform duration-200 ${
                            mobileDropdownOpen[item.name] ? "rotate-180" : ""
                          }`}
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M19 9l-7 7-7-7"
                          />
                        </svg>
                      </button>
                    </div>

                    {/* Mobile Dropdown Items - Only show when expanded */}
                    <div
                      className={`overflow-hidden transition-all duration-300 ease-in-out ${
                        mobileDropdownOpen[item.name]
                          ? "max-h-96 opacity-100"
                          : "max-h-0 opacity-0"
                      }`}
                    >
                      <div className="ml-4 mt-1 space-y-0.5 border-l-2 border-gray-100 pl-3">
                        {item.dropdownItems.map((dropdownItem) => (
                          <Link
                            key={dropdownItem.name}
                            to={dropdownItem.path}
                            onClick={closeMobileMenu}
                            className={`block px-2 py-1.5 text-xs rounded-md transition-colors ${
                              isActive(dropdownItem.path)
                                ? "text-primary-700 bg-primary-50 font-medium"
                                : "text-gray-600 hover:text-primary-700 hover:bg-gray-50"
                            }`}
                          >
                            {dropdownItem.name}
                          </Link>
                        ))}
                      </div>
                    </div>
                  </div>
                ) : (
                  <Link
                    to={item.path}
                    onClick={closeMobileMenu}
                    className={`block px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                      isActive(item.path)
                        ? "text-primary-700 bg-primary-50 border-l-4 border-primary-700"
                        : "text-gray-700 hover:text-primary-700 hover:bg-gray-50"
                    }`}
                  >
                    {item.name}
                  </Link>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
