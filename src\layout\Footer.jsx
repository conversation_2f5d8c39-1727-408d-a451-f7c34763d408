import { Link } from 'react-router-dom';
import { Icon } from '@iconify/react';
import { Button, Form, InputField, Checkbox } from '../components';
import footerBg from '../assets/footerbg.png';
import * as Yup from 'yup';

// Validation schema
const loginValidationSchema = Yup.object({
  email: Yup.string()
    .email('Invalid email address')
    .required('Email is required'),
  password: Yup.string()
    .required('Password is required'),
});

// Initial values
const initialValues = {
  email: '',
  password: '',
  rememberMe: false
};

const Footer = () => {
  const handleSubmit = (values, { setSubmitting }) => {
    console.log('Login submitted:', values);
    setSubmitting(false);
  };

  return (
    <footer
      className="text-white py-8 md:py-12 bg-cover bg-center bg-no-repeat relative"
      style={{ backgroundImage: `url(${footerBg})` }}
    >
      {/* Dark overlay for better text readability */}
      {/* <div className="absolute inset-0 bg-black opacity-60 pointer-events-none"></div> */}
      
      <div className="mx-auto px-4 sm:px-6 relative z-10 max-w-7xl">
        {/* Logo and Social Media Section */}
        {/* Logo and Social Media Section */}
        <div className="flex flex-col sm:flex-row justify-between items-center mb-6 md:mb-8 pb-4 relative space-y-4 sm:space-y-0">
          {/* Gradient border */}
          <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-black via-green-500 to-black"></div>
          
          {/* Logo Section */}
          <div className="flex items-center order-1 sm:order-none">
            <img src="/src/assets/BCDp.svg" alt="BCDP Logo" className="w-20 h-20 sm:w-24 sm:h-24 md:w-32 md:h-32" />
          </div>

          {/* Social Media Icons */}
          <div className="flex space-x-3 sm:space-x-4 order-2 sm:order-none">
            <a href="#" className="w-10 h-10 sm:w-12 sm:h-12 bg-white rounded-full flex items-center justify-center hover:bg-gray-100 transition-colors">
              <Icon icon="mdi:linkedin" className="text-black text-lg sm:text-2xl" />
            </a>
            <a href="#" className="w-10 h-10 sm:w-12 sm:h-12 bg-white rounded-full flex items-center justify-center hover:bg-gray-100 transition-colors">
              <Icon icon="mdi:facebook" className="text-black text-lg sm:text-2xl" />
            </a>
            <a href="#" className="w-10 h-10 sm:w-12 sm:h-12 bg-white rounded-full flex items-center justify-center hover:bg-gray-100 transition-colors">
              <Icon icon="mdi:youtube" className="text-black text-lg sm:text-2xl" />
            </a>
            <a href="#" className="w-10 h-10 sm:w-12 sm:h-12 bg-white rounded-full flex items-center justify-center hover:bg-gray-100 transition-colors">
              <Icon icon="mdi:email" className="text-black text-lg sm:text-2xl" />
            </a>
          </div>

          {/* Spacer for desktop alignment - hidden on mobile */}
          <div className="hidden lg:block w-20"></div>
        </div>

        {/* Main Content Grid */}
        <div className="space-y-8 lg:space-y-0 lg:grid lg:grid-cols-6 lg:gap-8 lg:items-start">
          
          {/* Login Form Section */}
          <div className="lg:col-span-2">
            <div className="bg-white/95 backdrop-blur-sm rounded-2xl p-6 w-full max-w-sm mx-auto lg:mx-0 shadow-xl">
              <h3 className="text-xl font-bold text-gray-800 mb-6 text-center">Login</h3>
              <Form
                initialValues={initialValues}
                validationSchema={loginValidationSchema}
                onSubmit={handleSubmit}
                submitText="Sign In"
                showReset={false}
                submitButtonProps={{
                  className: "w-full py-3 text-sm font-semibold text-white bg-gradient-to-r from-green-600 to-green-700 rounded-xl hover:from-green-700 hover:to-green-800 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                }}
              >
                <div className="space-y-4">
                  <InputField
                    name="email"
                    type="email"
                    placeholder="Email Address"
                    className="rounded-xl border-gray-200 focus:border-green-500 focus:ring-green-500"
                  />
                  <InputField
                    name="password"
                    type="password"
                    placeholder="Password"
                    showPasswordToggle={true}
                    className="rounded-xl border-gray-200 focus:border-green-500 focus:ring-green-500"
                  />
                </div>
                
                <div className="flex items-center justify-between my-4">
                  <Checkbox
                    name="rememberMe"
                    label="Remember Me"
                    className="text-sm text-gray-600"
                  />
                  <a href="#" className="text-sm font-medium text-green-600 hover:text-green-700 transition-colors">
                    Forgot password?
                  </a>
                </div>
              </Form>
            </div>
          </div>

          {/* Contact Information */}
          <div className="lg:col-span-1">
            <h3 className="text-xl font-bold mb-6 text-center lg:text-left text-green-300">Contact Info</h3>
            
            {/* Mobile: Card-style layout */}
            <div className="lg:hidden grid grid-cols-1 gap-4">
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20">
                <div className="flex items-start space-x-3">
                  <div className="w-10 h-10 bg-green-400 rounded-full flex items-center justify-center flex-shrink-0">
                    <Icon icon="mdi:map-marker" className="text-white text-lg" />
                  </div>
                  <div>
                    <p className="font-semibold text-green-300 text-sm mb-1">Main Office</p>
                    <p className="text-xs text-gray-200 leading-relaxed">123, abc, street,<br />Dhaka - 10000</p>
                  </div>
                </div>
              </div>
              
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20">
                <div className="flex items-start space-x-3">
                  <div className="w-10 h-10 bg-green-400 rounded-full flex items-center justify-center flex-shrink-0">
                    <Icon icon="mdi:map-marker" className="text-white text-lg" />
                  </div>
                  <div>
                    <p className="font-semibold text-green-300 text-sm mb-1">Showroom</p>
                    <p className="text-xs text-gray-200 leading-relaxed">123, abc, street,<br />Dhaka 10000</p>
                  </div>
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20">
                  <div className="flex flex-col items-center text-center space-y-2">
                    <div className="w-10 h-10 bg-green-400 rounded-full flex items-center justify-center">
                      <Icon icon="mdi:email" className="text-white text-lg" />
                    </div>
                    <div>
                      <p className="font-semibold text-green-300 text-xs mb-1">Email</p>
                      <p className="text-xs text-gray-200"><EMAIL></p>
                    </div>
                  </div>
                </div>
                
                <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20">
                  <div className="flex flex-col items-center text-center space-y-2">
                    <div className="w-10 h-10 bg-green-400 rounded-full flex items-center justify-center">
                      <Icon icon="mdi:phone" className="text-white text-lg" />
                    </div>
                    <div>
                      <p className="font-semibold text-green-300 text-xs mb-1">Call</p>
                      <p className="text-xs text-gray-200">012###########</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Desktop: Original layout */}
            <div className="hidden lg:block space-y-4">
              <div className="flex items-start space-x-3">
                <Icon icon="mdi:map-marker" className="text-green-400 text-lg mt-0.5 flex-shrink-0" />
                <div>
                  <p className="font-medium text-sm">Main Office</p>
                  <p className="text-xs text-gray-200">123, abc, street,</p>
                  <p className="text-xs text-gray-200">Dhaka - 10000</p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <Icon icon="mdi:map-marker" className="text-green-400 text-lg mt-0.5 flex-shrink-0" />
                <div>
                  <p className="font-medium text-sm">Showroom</p>
                  <p className="text-xs text-gray-200">123, abc, street,</p>
                  <p className="text-xs text-gray-200">Dhaka 10000</p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <Icon icon="mdi:email" className="text-green-400 text-lg mt-0.5 flex-shrink-0" />
                <div>
                  <p className="font-medium text-sm">Email Us</p>
                  <p className="text-xs text-gray-200"><EMAIL></p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <Icon icon="mdi:phone" className="text-green-400 text-lg mt-0.5 flex-shrink-0" />
                <div>
                  <p className="font-medium text-sm">Call Us</p>
                  <p className="text-xs text-gray-200">012###########</p>
                </div>
              </div>
            </div>
          </div>

          {/* Categories Section */}
          <div className="lg:col-span-1">
            <h3 className="text-xl font-bold mb-6 text-center lg:text-left text-green-300">Categories</h3>
            
            {/* Mobile: Improved card layout */}
            <div className="lg:hidden">
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20">
                <div className="grid grid-cols-2 gap-4">
                  {[1, 2, 3, 4].map((num) => (
                    <a key={num} href="#" className="flex flex-col items-center space-y-2 p-2 rounded-lg hover:bg-white/10 transition-colors">
                      <div className="w-8 h-8 bg-green-400 rounded-full flex items-center justify-center">
                        <Icon icon="mdi:shape-outline" className="text-white text-sm" />
                      </div>
                      <span className="text-xs font-medium text-center">Category {num}</span>
                    </a>
                  ))}
                </div>
              </div>
            </div>
            
            {/* Desktop: Original layout */}
            <div className="hidden lg:block">
              <ul className="space-y-2">
                <li><a href="#" className="text-sm text-gray-200 hover:text-white transition-colors">Category 1</a></li>
                <li><a href="#" className="text-sm text-gray-200 hover:text-white transition-colors">Category 2</a></li>
                <li><a href="#" className="text-sm text-gray-200 hover:text-white transition-colors">Category 3</a></li>
                <li><a href="#" className="text-sm text-gray-200 hover:text-white transition-colors">Category 4</a></li>
              </ul>
            </div>
          </div>

          {/* Quick Links Section */}
          <div className="lg:col-span-1">
            <h3 className="text-xl font-bold mb-6 text-center lg:text-left text-green-300">Quick Links</h3>
            
            {/* Mobile: Improved card layout */}
            <div className="lg:hidden">
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20">
                <div className="grid grid-cols-2 gap-3">
                  {[
                    { icon: 'mdi:information-outline', text: 'About Us', to: '/about' },
                    { icon: 'mdi:tools', text: 'Services', to: '/services' },
                    { icon: 'mdi:handshake-outline', text: 'Partners', to: '/partners' },
                    { icon: 'mdi:email-outline', text: 'Contact', to: '/contact' },
                    { icon: 'mdi:shield-outline', text: 'Privacy', to: '/privacy' }
                  ].map((link, index) => (
                    <Link key={index} to={link.to} className="flex flex-col items-center space-y-2 p-2 rounded-lg hover:bg-white/10 transition-colors">
                      <div className="w-8 h-8 bg-green-400 rounded-full flex items-center justify-center">
                        <Icon icon={link.icon} className="text-white text-sm" />
                      </div>
                      <span className="text-xs font-medium text-center">{link.text}</span>
                    </Link>
                  ))}
                </div>
              </div>
            </div>
            
            {/* Desktop: Original layout */}
            <div className="hidden lg:block">
              <ul className="space-y-2">
                <li><Link to="/about" className="text-sm text-gray-200 hover:text-white transition-colors">About Us</Link></li>
                <li><Link to="/services" className="text-sm text-gray-200 hover:text-white transition-colors">Services</Link></li>
                <li><Link to="/partners" className="text-sm text-gray-200 hover:text-white transition-colors">Partners</Link></li>
                <li><Link to="/contact" className="text-sm text-gray-200 hover:text-white transition-colors">Contact Us</Link></li>
                <li><Link to="/privacy" className="text-sm text-gray-200 hover:text-white transition-colors">Privacy Policy</Link></li>
              </ul>
            </div>
          </div>

          {/* Others Section */}
          <div className="lg:col-span-1">
            <h3 className="text-xl font-bold mb-6 text-center lg:text-left text-green-300">Others</h3>
            
            {/* Mobile: Improved card layout */}
            <div className="lg:hidden">
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20">
                <div className="grid grid-cols-2 gap-4">
                  <a href="#" className="flex flex-col items-center space-y-2 p-2 rounded-lg hover:bg-white/10 transition-colors">
                    <div className="w-8 h-8 bg-green-400 rounded-full flex items-center justify-center">
                      <Icon icon="mdi:book-open-outline" className="text-white text-sm" />
                    </div>
                    <span className="text-xs font-medium text-center">Stories</span>
                  </a>
                  
                  <a href="#" className="flex flex-col items-center space-y-2 p-2 rounded-lg hover:bg-white/10 transition-colors">
                    <div className="w-8 h-8 bg-green-400 rounded-full flex items-center justify-center">
                      <Icon icon="mdi:star-outline" className="text-white text-sm" />
                    </div>
                    <span className="text-xs font-medium text-center">Reviews</span>
                  </a>
                </div>
              </div>
            </div>
            
            {/* Desktop: Original layout */}
            <div className="hidden lg:block">
              <ul className="space-y-2">
                <li><a href="#" className="text-sm text-gray-200 hover:text-white transition-colors">Stories</a></li>
                <li><a href="#" className="text-sm text-gray-200 hover:text-white transition-colors">Customer Reviews</a></li>
              </ul>
            </div>
          </div>
        </div>

        {/* Copyright Section */}
      <div className="mt-8 pt-4 border-t border-white/20 text-center">
          <p className="text-sm text-gray-300">
            © {new Date().getFullYear()} BCDP. All rights reserved. | 
            <Link to="/privacy" className="text-green-300 hover:text-green-200 ml-1">Privacy Policy</Link> | 
            <Link to="/terms" className="text-green-300 hover:text-green-200 ml-1">Terms of Service</Link>
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;