import { useField } from 'formik';
import Text from '../Text';

const TextArea = ({ 
  label, 
  placeholder, 
  rows = 4,
  className = '', 
  labelClassName = '',
  errorClassName = '',
  required = false,
  disabled = false,
  resize = true,
  ...props 
}) => {
  const [field, meta] = useField(props);
  const hasError = meta.touched && meta.error;

  return (
    <div className={`mb-3 ${className}`}>
      {label && (
        <label
          htmlFor={props.name}
          className={`block mb-1 ${labelClassName}`}
        >
          <Text variant="body" weight="medium" className="text-gray-700">
            {label}
            {required && <span className="text-red-500 ml-1">*</span>}
          </Text>
        </label>
      )}
      
      <textarea
        {...field}
        {...props}
        rows={rows}
        placeholder={placeholder}
        disabled={disabled}
        className={`
          w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent
          ${hasError 
            ? 'border-red-500 bg-red-50' 
            : 'border-gray-300 bg-white hover:border-gray-400'
          }
          ${disabled ? 'bg-gray-100 cursor-not-allowed' : ''}
          ${!resize ? 'resize-none' : 'resize-y'}
          transition-colors duration-200
        `}
      />
      
      {hasError && (
        <Text 
          variant="small" 
          className={`text-red-500 mt-1 ${errorClassName}`}
        >
          {meta.error}
        </Text>
      )}
    </div>
  );
};

export default TextArea;
