import React from 'react';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line, AreaChart, Area } from 'recharts';

// Data for Energy Sector (Line Chart) - Top Left
const energyData = [
  { year: '2022', value: 2000000 },
  { year: '2023', value: 1500000 },
  { year: '2024', value: 2200000 },
  { year: '2025', value: 1800000 },
  { year: '2026', value: 2500000 }
];

// Data for IPPU Sector (Stacked Bar Chart) - Top Right
const ippuData = [
  {
    year: '2022',
    'Estimated fund requirement as per NAP': 1400000,
    'Investment already made': 1800000,
    'Total investment (completed and ongoing)': 1600000,
    'Financing gap': 2000000
  },
  {
    year: '2023',
    'Estimated fund requirement as per NAP': 1200000,
    'Investment already made': 1500000,
    'Total investment (completed and ongoing)': 1300000,
    'Financing gap': 1700000
  },
  {
    year: '2024',
    'Estimated fund requirement as per NAP': 1600000,
    'Investment already made': 1900000,
    'Total investment (completed and ongoing)': 1700000,
    'Financing gap': 2100000
  },
  {
    year: '2025',
    'Estimated fund requirement as per NAP': 1800000,
    'Investment already made': 2200000,
    'Total investment (completed and ongoing)': 2000000,
    'Financing gap': 2400000
  }
];

// Data for AFOLU Sector (Area Chart with Line) - Bottom Left
const afoluData = [
  { year: '2022', value: 2200000 },
  { year: '2024', value: 2050000 },
  { year: '2026', value: 2200000 },
  { year: '2028', value: 1950000 },
  { year: '2030', value: 2100000 }
];

// Data for IPPU Achievement Chart (Grouped Bar Chart) - Bottom Right
const ippuAchievementData = [
  {
    year: '2022',
    'Target to be achieved by 2025': 1200000,
    'Achieved through completed investments': 400000,
    'Planned achievement through ongoing investments': 600000,
    'Financing gap': 800000
  },
  {
    year: '2023',
    'Target to be achieved by 2025': 2000000,
    'Achieved through completed investments': 600000,
    'Planned achievement through ongoing investments': 800000,
    'Financing gap': 1000000
  },
  {
    year: '2024',
    'Target to be achieved by 2025': 1500000,
    'Achieved through completed investments': 500000,
    'Planned achievement through ongoing investments': 700000,
    'Financing gap': 900000
  },
  {
    year: '2025',
    'Target to be achieved by 2025': 1800000,
    'Achieved through completed investments': 700000,
    'Planned achievement through ongoing investments': 900000,
    'Financing gap': 1100000
  }
];

const formatValue = (value) => {
  return (value / 1000000).toFixed(1) + 'M';
};

const CustomTooltip = ({ active, payload, label }) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-white p-3 border border-gray-200 rounded shadow-lg">
        <p className="font-medium text-sm">{`Year: ${label}`}</p>
        {payload.map((entry, index) => (
          <p key={index} style={{ color: entry.color }} className="text-xs">
            {`${entry.dataKey}: $${formatValue(entry.value)}`}
          </p>
        ))}
      </div>
    );
  }
  return null;
};

const ChartCard = ({ title, children, legendItems = [] }) => (
  <div className="bg-white rounded-lg border border-gray-200 p-4">
    <div className="flex justify-between items-start mb-4">
      <h3 className="text-base font-semibold text-green-600 flex-1 pr-4 leading-tight">{title}</h3>
      {legendItems.length > 0 && (
        <div className="flex flex-col gap-1 text-xs min-w-max">
          {legendItems.map((item, index) => (
            <div key={index} className="flex items-center">
              <div 
                className="w-3 h-3 mr-2 rounded-sm" 
                style={{ backgroundColor: item.color }}
              ></div>
              <span className="text-gray-600 text-xs">{item.label}</span>
            </div>
          ))}
        </div>
      )}
    </div>
    <div className="h-64">
      {children}
    </div>
  </div>
);

const ClimateFundDashboard = () => {
  const energyLegend = [
    { color: '#059669', label: 'Investment Trend' }
  ];

  const ippuLegend = [
    { color: '#065f46', label: 'Estimated fund requirement as per NAP' },
    { color: '#059669', label: 'Investment already made' },
    { color: '#10b981', label: 'Total investment (completed and ongoing)' },
    { color: '#6ee7b7', label: 'Financing gap' }
  ];

  const afoluLegend = [
    { color: '#059669', label: 'CO2 Mitigation Trend' }
  ];

  const achievementLegend = [
    { color: '#1e40af', label: 'Target to be achieved by 2025' },
    { color: '#059669', label: 'Achieved through completed investments' },
    { color: '#0891b2', label: 'Planned achievement through ongoing investments' },
    { color: '#4338ca', label: 'Financing gap' }
  ];

  return (
    <div className="w-full min-h-screen bg-gray-50 p-4">
      <div className="max-w-7xl mx-auto">
        <h1 className="text-xl font-bold text-center mb-6 text-gray-800">
          Climate Fund Investment By Sector (Combined By Domestic And External Finance)
        </h1>
        
        <div className="grid grid-cols-2 gap-4">
          {/* Top Left - Energy Sector Line Chart */}
          <ChartCard 
            title="Mitigation From Energy (Power, Industry, Transport) Sector"
            legendItems={energyLegend}
          >
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={energyData} margin={{ top: 5, right: 5, left: 5, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" />
                <XAxis 
                  dataKey="year" 
                  tick={{ fontSize: 10, fill: '#6b7280' }}
                  axisLine={{ stroke: '#e5e7eb' }}
                />
                <YAxis 
                  tick={{ fontSize: 10, fill: '#6b7280' }}
                  axisLine={{ stroke: '#e5e7eb' }}
                  tickFormatter={formatValue}
                />
                <Tooltip content={<CustomTooltip />} />
                <Line 
                  type="monotone" 
                  dataKey="value" 
                  stroke="#059669" 
                  strokeWidth={2}
                  dot={{ fill: '#059669', strokeWidth: 2, r: 4 }}
                />
              </LineChart>
            </ResponsiveContainer>
          </ChartCard>

          {/* Top Right - IPPU Sector Stacked Bar Chart */}
          <ChartCard 
            title="Mitigation From Industrial Process And Product Use (IPPU)"
            legendItems={ippuLegend}
          >
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={ippuData} margin={{ top: 5, right: 5, left: 5, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" />
                <XAxis 
                  dataKey="year" 
                  tick={{ fontSize: 10, fill: '#6b7280' }}
                  axisLine={{ stroke: '#e5e7eb' }}
                />
                <YAxis 
                  tick={{ fontSize: 10, fill: '#6b7280' }}
                  axisLine={{ stroke: '#e5e7eb' }}
                  tickFormatter={formatValue}
                />
                <Tooltip content={<CustomTooltip />} />
                <Bar dataKey="Estimated fund requirement as per NAP" stackId="a" fill="#065f46" />
                <Bar dataKey="Investment already made" stackId="a" fill="#059669" />
                <Bar dataKey="Total investment (completed and ongoing)" stackId="a" fill="#10b981" />
                <Bar dataKey="Financing gap" stackId="a" fill="#6ee7b7" />
              </BarChart>
            </ResponsiveContainer>
          </ChartCard>

          {/* Bottom Left - AFOLU Sector Area Chart with Line */}
          <ChartCard 
          title="Mitigation From Agriculture, Forestry And Other Land Use (AFOLU)"
          legendItems={afoluLegend}
        >
          <ResponsiveContainer width="100%" height="100%">
            <AreaChart data={afoluData} margin={{ top: 5, right: 5, left: 5, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" />
              <XAxis 
                dataKey="year" 
                tick={{ fontSize: 12, fill: '#6b7280' }}
                axisLine={{ stroke: '#e5e7eb' }}
              />
              <YAxis 
                label={{ value: 'CO2 Mitigation (Mt)', angle: -90, position: 'insideLeft', style: { textAnchor: 'middle', fontSize: '12px', fill: '#6b7280' } }}
                tick={{ fontSize: 12, fill: '#6b7280' }}
                axisLine={{ stroke: '#e5e7eb' }}
                tickFormatter={formatValue}
              />
              <Tooltip content={<CustomTooltip />} />
              <defs>
                <linearGradient id="afoluGradient" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="0%" stopColor="rgba(159, 255, 169, 0.9)" stopOpacity={1}/>
                  <stop offset="100%" stopColor="rgba(218, 247, 214, 0.9)" stopOpacity={1}/>
                </linearGradient>
              </defs>
              <Area 
                type="monotone" 
                dataKey="value" 
                stroke="#059669" 
                fillOpacity={1}
                fill="url(#afoluGradient)"
                strokeWidth={2}
                dot={{ fill: '#059669', strokeWidth: 2, r: 4 }}
                activeDot={{ r: 6, fill: '#059669' }}
              />
            </AreaChart>
          </ResponsiveContainer>
        </ChartCard>

          {/* Bottom Right - IPPU Achievement Grouped Bar Chart */}
          <ChartCard 
            title="Mitigation From Industrial Process And Product Use (IPPU) - Achievement Analysis"
            legendItems={achievementLegend}
          >
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={ippuAchievementData} margin={{ top: 5, right: 5, left: 5, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" />
                <XAxis 
                  dataKey="year" 
                  tick={{ fontSize: 10, fill: '#6b7280' }}
                  axisLine={{ stroke: '#e5e7eb' }}
                />
                <YAxis 
                  tick={{ fontSize: 10, fill: '#6b7280' }}
                  axisLine={{ stroke: '#e5e7eb' }}
                  tickFormatter={formatValue}
                />
                <Tooltip content={<CustomTooltip />} />
                <Bar dataKey="Target to be achieved by 2025" fill="#1e40af" />
                <Bar dataKey="Achieved through completed investments" fill="#059669" />
                <Bar dataKey="Planned achievement through ongoing investments" fill="#0891b2" />
                <Bar dataKey="Financing gap" fill="#4338ca" />
              </BarChart>
            </ResponsiveContainer>
          </ChartCard>
        </div>
      </div>
    </div>
  );
};

export default ClimateFundDashboard;