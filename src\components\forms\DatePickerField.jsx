import { useField } from "formik";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import Text from "../Text";

const DatePickerField = ({
  label,
  required = false,
  className = "",
  labelClassName = "",
  errorClassName = "",
  disabled = false,
  ...props
}) => {
  const [field, meta, helpers] = useField(props);
  const hasError = meta.touched && meta.error;

  return (
    <div className={`mb-4 ${className}`}>
      {label && (
        <label htmlFor={props.name} className={`block mb-2 ${labelClassName}`}>
          <Text variant="body" weight="medium" className="text-gray-700">
            {label}
            {required && <span className="text-red-500 ml-1">*</span>}
          </Text>
        </label>
      )}

      <div className="relative">
        <DatePicker
          {...props}
          selected={field.value ? new Date(field.value) : null}
          onChange={(date) => helpers.setValue(date ? date.toISOString() : "")}
          dateFormat="yyyy-MM-dd"
          className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent
            ${
              hasError
                ? "border-red-500 bg-red-50"
                : "border-gray-300 bg-white hover:border-gray-400"
            }
            ${disabled ? "bg-gray-100 cursor-not-allowed" : ""}`}
        />
      </div>

      {hasError && (
        <Text variant="small" className={`text-red-500 mt-1 ${errorClassName}`}>
          {meta.error}
        </Text>
      )}
    </div>
  );
};

export default DatePickerField;
