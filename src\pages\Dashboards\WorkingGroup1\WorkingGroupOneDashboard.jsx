import React from "react";
import { useSelector } from "react-redux";
import Graph1 from "./Graph1";

const SummaryCard = ({ title, value }) => {
  return (
    <div
      className="p-4 rounded-lg flex flex-col justify-between w-full lg:w-[250px] lg:h-[136px]"
      style={{
        backgroundColor: "#EDFFEA",
      }}
    >
      <p className="text-3xl font-bold text-green-700">{value}</p>
      <div className="flex justify-between items-center">
        <p className="text-gray-800 text-[15px] font-semibold text-base">
          {title}
        </p>
        <a href="#!" className="text-sm text-green-700 text-[12px]">
          View Details
        </a>
      </div>
    </div>
  );
};

const WorkingGroupOneDashboard = () => {
  const user = useSelector((state) => state.auth?.user);
  console.log(user);
  const summaryData = [
    {
      title: "Total Climate Projects",
      value: 25,
    },
    {
      title: "Projects With Domestic Finance",
      value: 25,
    },
    {
      title: "Domestic Fund Of Total Fund",
      value: "50%",
    },
    {
      title: "Completed Projects With Domestic Finance",
      value: 10,
    },
    {
      title: "Ongoing Projects With Domestic Finance",
      value: 15,
    },
  ];
  return (
    <div className="max-w-container mx-auto">
      <div>
        <h2 class="text-[32px]">
          Dashboard{" "}
          <span className="text-[22px] text-[#A3A3A3]">
            (Climate Finance -Domestic)
          </span>
        </h2>
        <p className="text-[#A3A3A3] text-[18px]">
          Hi {user?.username}. Welcome to your Dashboard
        </p>
      </div>
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4">
        {summaryData.map((item, index) => (
          <SummaryCard
            key={index}
            title={item.title}
            value={item.value}
            description={item.description}
          />
        ))}
      </div>

        <section className="py-8 md:py-16 lg:py-10 bg-white">
        <div className="max-w-container mx-auto px-4">
          <Graph1 />
        </div>
      </section>

    </div>
  );
};

export default WorkingGroupOneDashboard;
