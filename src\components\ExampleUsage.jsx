import { useState } from 'react';
import { useTanstackQuery, useTanstackMutation } from '../hooks/useTanstackQuery';
import { 
  useGetApiQuery, 
  usePostApiMutation, 
  useUpdateApiJsonMutation, 
  useDeleteApiMutation 
} from '../store/api/commonApi';
import { useAuth } from '../hooks/useAuth';

const ExampleUsage = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [page, setPage] = useState(1);
  const { login } = useAuth();

  // Example using TanStack Query for data fetching
  const {
    data: tanstackData,
    isLoading: tanstackLoading,
    error: tanstackError,
    refetch: tanstackRefetch
  } = useTanstackQuery({
    queryKey: 'example-data',
    endpoint: 'example-endpoint',
    params: {
      search: searchTerm,
      page: page,
      limit: 10
    },
    enabled: true, // Only fetch when this is true
  });

  // Example using TanStack Mutation
  const tanstackMutation = useTanstackMutation({
    onSuccess: (data) => {
      console.log('TanStack mutation success:', data);
    },
    onError: (error) => {
      console.error('TanStack mutation error:', error);
    },
    invalidateQueries: ['example-data'], // Invalidate these queries after mutation
  });

  // Example using RTK Query for data fetching
  const {
    data: rtkData,
    isLoading: rtkLoading,
    error: rtkError,
    refetch: rtkRefetch
  } = useGetApiQuery({
    url: 'example-endpoint',
    params: {
      search: searchTerm,
      page: page,
      limit: 10
    }
  });

  // Example using RTK Query mutations
  const [postApi] = usePostApiMutation();
  const [updateApi] = useUpdateApiJsonMutation();
  const [deleteApi] = useDeleteApiMutation();

  // Example handlers
  const handleTanstackMutation = () => {
    tanstackMutation.mutate({
      endpoint: 'example-endpoint',
      method: 'POST',
      body: { name: 'Test', description: 'Test description' }
    });
  };

  const handleRtkPost = async () => {
    try {
      const result = await postApi({
        end_point: 'example-endpoint',
        body: { name: 'Test', description: 'Test description' }
      }).unwrap();
      console.log('RTK Post success:', result);
    } catch (error) {
      console.error('RTK Post error:', error);
    }
  };

  const handleRtkUpdate = async () => {
    try {
      const result = await updateApi({
        end_point: 'example-endpoint/1',
        body: { name: 'Updated Test', description: 'Updated description' }
      }).unwrap();
      console.log('RTK Update success:', result);
    } catch (error) {
      console.error('RTK Update error:', error);
    }
  };

  const handleRtkDelete = async () => {
    try {
      const result = await deleteApi({
        end_point: 'example-endpoint/1',
        body: {}
      }).unwrap();
      console.log('RTK Delete success:', result);
    } catch (error) {
      console.error('RTK Delete error:', error);
    }
  };

  const handleLogin = async () => {
    const result = await login({
      email: '<EMAIL>',
      password: 'password123'
    });
    
    if (result.success) {
      console.log('Login successful:', result.data);
    } else {
      console.error('Login failed:', result.error);
    }
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">TanStack Query & RTK Query Example</h1>
      
      {/* Search and Pagination Controls */}
      <div className="mb-6 space-y-4">
        <div>
          <label className="block text-sm font-medium mb-2">Search:</label>
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="border rounded px-3 py-2 w-full max-w-md"
            placeholder="Enter search term..."
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium mb-2">Page:</label>
          <input
            type="number"
            value={page}
            onChange={(e) => setPage(parseInt(e.target.value) || 1)}
            className="border rounded px-3 py-2 w-20"
            min="1"
          />
        </div>
      </div>

      {/* TanStack Query Section */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">TanStack Query</h2>
        <div className="space-y-2">
          <p>Loading: {tanstackLoading ? 'Yes' : 'No'}</p>
          <p>Error: {tanstackError ? tanstackError.message : 'None'}</p>
          <p>Data: {tanstackData ? JSON.stringify(tanstackData, null, 2) : 'No data'}</p>
          <button
            onClick={tanstackRefetch}
            className="bg-blue-500 text-white px-4 py-2 rounded mr-2"
          >
            Refetch
          </button>
          <button
            onClick={handleTanstackMutation}
            className="bg-green-500 text-white px-4 py-2 rounded"
            disabled={tanstackMutation.isPending}
          >
            {tanstackMutation.isPending ? 'Creating...' : 'Create (TanStack)'}
          </button>
        </div>
      </div>

      {/* RTK Query Section */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">RTK Query</h2>
        <div className="space-y-2">
          <p>Loading: {rtkLoading ? 'Yes' : 'No'}</p>
          <p>Error: {rtkError ? JSON.stringify(rtkError) : 'None'}</p>
          <p>Data: {rtkData ? JSON.stringify(rtkData, null, 2) : 'No data'}</p>
          <div className="space-x-2">
            <button
              onClick={rtkRefetch}
              className="bg-blue-500 text-white px-4 py-2 rounded"
            >
              Refetch
            </button>
            <button
              onClick={handleRtkPost}
              className="bg-green-500 text-white px-4 py-2 rounded"
            >
              Create (RTK)
            </button>
            <button
              onClick={handleRtkUpdate}
              className="bg-yellow-500 text-white px-4 py-2 rounded"
            >
              Update (RTK)
            </button>
            <button
              onClick={handleRtkDelete}
              className="bg-red-500 text-white px-4 py-2 rounded"
            >
              Delete (RTK)
            </button>
          </div>
        </div>
      </div>

      {/* Authentication Section */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Authentication</h2>
        <button
          onClick={handleLogin}
          className="bg-purple-500 text-white px-4 py-2 rounded"
        >
          Test Login
        </button>
      </div>
    </div>
  );
};

export default ExampleUsage;
