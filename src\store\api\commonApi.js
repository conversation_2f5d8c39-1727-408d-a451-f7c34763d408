import { apiSlice } from "./apiSlice";

export const commonApi = apiSlice.injectEndpoints({
    reducerPath: "commonSliceApi",
    tagTypes: ["LIST_API", "Menu"],
    endpoints: (builder) => ({
        // Get menu items
        getMenuItems: builder.query({
            query: () => ({
                url: "details",
                method: "GET",
            }),
            providesTags: ["Menu", "LIST_API"],
        }),

        // Get API with ID
        getApiWithId: builder.query({
            query: ([url, id]) => ({
                url: `${url}/${id}`,
                method: "GET",
            }),
            providesTags: ["LIST_API"],
        }),

        // Get API with params (for searching and pagination)
        getApi: builder.query({
            query: ({ url, params }) => {
                const queryObject = {
                    url,
                    method: "GET",
                };
                if (params) {
                    queryObject.params = params;
                }
                return queryObject;
            },
            providesTags: ["LIST_API"],
        }),

        // POST API
        postApi: builder.mutation({
            query: (data) => {
                return {
                    url: data.end_point,
                    method: "POST",
                    body: data.body,
                };
            },
            invalidatesTags: ["LIST_API"],
        }),

        // Update API with JSON (PUT method)
        updateApiJson: builder.mutation({
            query: (data) => ({
                url: data.end_point,
                method: "PUT",
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data.body),
            }),
            invalidatesTags: ["LIST_API"],
        }),

        // Update API with FormData (POST with _method=PUT)
        updateApi: builder.mutation({
            query: (data) => {
                // For FormData, append _method for Laravel-style method spoofing
                if (data.body instanceof FormData) {
                    data.body.append("_method", "PUT");
                }
                return {
                    url: data.end_point,
                    method: "POST",
                    body: data.body,
                };
            },
            invalidatesTags: ["LIST_API"],
        }),

        // Update FormData API (without method spoofing)
        updateFormDataApi: builder.mutation({
            query: (data) => {
                return {
                    url: data.end_point,
                    method: "POST",
                    body: data.body,
                };
            },
            invalidatesTags: ["LIST_API"],
        }),

        // Delete API
        deleteApi: builder.mutation({
            query: (data) => {
                return {
                    url: data.end_point,
                    method: "DELETE",
                    body: data.body,
                };
            },
            invalidatesTags: ["LIST_API"],
        }),

        // Login endpoint
        login: builder.mutation({
            query: (credentials) => ({
                url: "/login/",
                method: "POST",
                body: credentials,
            }),
        }),

        // Logout endpoint
        logoutApi: builder.mutation({
            query: () => ({
                url: "/logout-open/",
                method: "POST",
            }),
        }),
    }),
});

export const {
    useGetApiQuery,
    useGetApiWithIdQuery,
    useUpdateApiJsonMutation,
    usePostApiMutation,
    useUpdateApiMutation,
    useUpdateFormDataApiMutation,
    useDeleteApiMutation,
    useGetMenuItemsQuery,
    useLoginMutation,
    useLogoutApiMutation,
} = commonApi;
