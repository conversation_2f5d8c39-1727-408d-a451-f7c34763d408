import { Formik, Form as FormikForm } from 'formik';
import Button from '../Button';
import Text from '../Text';

const Form = ({
  initialValues,
  validationSchema,
  onSubmit,
  children,
  submitText = 'Submit',
  resetText = 'Reset',
  showReset = false,
  showSubmit = true,
  showActions = true,
  customActions = null,
  submitButtonProps = {},
  resetButtonProps = {},
  className = '',
  formClassName = '',
  actionsClassName = '',
  enableReinitialize = false,
  validateOnChange = true,
  validateOnBlur = true,
  onFieldChange = null,
  autoComplete = 'on',
  ...props
}) => {
  return (
    <div className={`w-full ${className}`}>
      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={onSubmit}
        enableReinitialize={enableReinitialize}
        validateOnChange={validateOnChange}
        validateOnBlur={validateOnBlur}
        {...props}
      >
        {(formikProps) => {
          const { isSubmitting, isValid, dirty, resetForm, errors, touched, status, values } = formikProps;

          // Call onFieldChange when values change
          if (onFieldChange && typeof onFieldChange === 'function') {
            onFieldChange(values, formikProps);
          }

          return (
            <FormikForm className={`space-y-4 ${formClassName}`} autoComplete={autoComplete}>
              {typeof children === 'function'
                ? children(formikProps)
                : children
              }

              {/* Form Actions */}
              {showActions && (
                <div className={`flex gap-4 pt-4 ${actionsClassName}`}>
                  {customActions ? (
                    customActions(formikProps)
                  ) : (
                    <>
                      {showSubmit && (
                        <Button
                          type="submit"
                          disabled={isSubmitting || !isValid}
                          className="flex-1 sm:flex-none"
                          {...submitButtonProps}
                        >
                          {isSubmitting ? 'Submitting...' : submitText}
                        </Button>
                      )}

                      {showReset && (
                        <Button
                          type="button"
                          variant="outline"
                          onClick={() => resetForm()}
                          disabled={isSubmitting || !dirty}
                          {...resetButtonProps}
                        >
                          {resetText}
                        </Button>
                      )}
                    </>
                  )}
                </div>
              )}
            </FormikForm>
          );
        }}
      </Formik>
    </div>
  );
};

export default Form;
