import React from 'react';
import { Chart } from "react-google-charts";

const ClimateFinanceDashboard = () => {

      const [isVisible, setIsVisible] = React.useState(true);
  // Sample data for adaptation projects by year
  const adaptationData = [
    ['Year', 'Projects'],
    ['2015', 400000],
    ['2016', 250000],
    ['2017', 400000],
    ['2018', 550000],
    ['2019', 500000],
    ['2020', 450000],
    ['2021', 250000],
    ['2022', 520000],
    ['2023', 480000],
    ['2024', 350000]
  ];

  // Sample data for mitigation projects by year
  const mitigationData = [
    ['Year', 'Projects'],
    ['2015', 400000],
    ['2016', 200000],
    ['2017', 400000],
    ['2018', 600000],
    ['2019', 450000],
    ['2020', 430000],
    ['2021', 230000],
    ['2022', 520000],
    ['2023', 450000],
    ['2024', 350000]
  ];

  // Pie chart data for adaptation fund breakdown
  const adaptationPieData = [
    ['Fund Type', 'Amount'],
    ['Adaptation Fund', 75],
    ['Total Fund', 25]
  ];

  // Pie chart data for mitigation fund breakdown
  const mitigationPieData = [
    ['Fund Type', 'Amount'],
    ['Mitigation Fund', 75],
    ['Total Fund', 25]
  ];

  const barChartOptions = {
    backgroundColor: 'transparent',
    chartArea: { left: 60, top: 40, width: '80%', height: '70%' },
    hAxis: {
      textStyle: { fontSize: 11, color: '#333' },
      gridlines: { color: 'transparent' }
    },
    vAxis: {
      format: '0',
      textStyle: { fontSize: 11, color: '#333' },
      gridlines: { color: '#f0f0f0' }
    },
    legend: { position: 'none' },
    bar: { groupWidth: '70%' },
    titleTextStyle: { fontSize: 13, color: '#333', bold: false }
  };

  const adaptationBarOptions = {
    ...barChartOptions,
    colors: ['#4CAF50'],
    title: 'Number of total adaptation projects with domestic finance:'
  };

  const mitigationBarOptions = {
    ...barChartOptions,
    colors: ['#9C27B0'],
    title: 'Number of total mitigation projects with domestic finance:'
  };

  const pieChartOptions = {
    is3D: true,
    legend: "none",
    backgroundColor: "transparent",
    chartArea: {
      left: 20,
      top: 20,
      width: "80%",
      height: "70%",
    },
    pieSliceText: "none",
    pieSliceTextStyle: {
      color: "#1F2937",
      fontSize: 10,
      bold: true,
    },
  };

  const adaptationPieOptions = {
    ...pieChartOptions,
    colors: ['#4CAF50', '#A5D6A7']
  };

  const mitigationPieOptions = {
    ...pieChartOptions,
    colors: ['#9C27B0', '#CE93D8']
  };

  return (
    <div className="w-full max-w-7xl mx-auto p-4">
      {/* Adaptation Section */}
      <div className="bg-white h-[450px] rounded-lg overflow-hidden">
        <div className="grid grid-cols-12  p-5 rounded shadow-xl gap-3">
          {/* Bar Chart Section */}
          <div className="col-span-8 p-4 border-r-2 rounded shadow-lg h-[420px]">
            <Chart
              chartType="ColumnChart"
              width="100%"
              height="100%"
              data={adaptationData}
              options={adaptationBarOptions}
            />
          </div>
          
          {/* Right Side Section */}
          <div className="col-span-4 flex flex-col h-[500px] ">
            {/* Stats Card */}
            <div className= "bg-white rounded shadow-xl p-4 ">
           <div className="relative w-[100x] h-24 bg-green-100 rounded-lg p-4 flex flex-col justify-between border border-green-200">
      {/* Toggle visibility button */}
      <button 
        onClick={() => setIsVisible(!isVisible)}
        className="absolute top-3 right-3 text-gray-400 hover:text-gray-600 transition-colors"
      >
        {isVisible ? (
          <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
            <circle cx="12" cy="12" r="3"/>
          </svg>
        ) : (
          <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"/>
            <line x1="1" y1="1" x2="23" y2="23"/>
          </svg>
        )}
      </button>

      {/* Main content */}
      <div className="flex flex-col">
        {/* Amount */}
        <div className="text-2xl font-bold text-green-700 mb-1">
          250000000৳
        </div>
        
        {/* Description */}
        <div className="text-sm text-gray-600 leading-tight mb-3">
          Total Adaptation Projects With<br />
          Domestic Finance
        </div>
      </div>

      {/* View Details link */}
      <div className="flex justify-end">
        <button className="text-sm text-blue-600 hover:text-blue-800 font-medium transition-colors">
          View Details
        </button>
      </div>
    </div>
            
            {/* Pie Chart Section */}
            <div className="flex-1 p-4 bg-white">
              <div className="mb-2">
                <h4 className="text-sm font-medium text-green-600 mb-1">Percentage Of Total Project Funds:</h4>
                <div className="flex items-center gap-4 text-xs mb-3">
                  <div className="flex items-center gap-1">
                    <div className="w-3 h-3 bg-green-500 rounded-sm"></div>
                    <span>Adaptation Fund</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <div className="w-3 h-3 bg-green-300 rounded-sm"></div>
                    <span>Total Fund</span>
                  </div>
                </div>
              </div>
              <div className="relative">
                <Chart
                  chartType="PieChart"
                  width="100%"
                  height="200px"
                  data={adaptationPieData}
                  options={adaptationPieOptions}
                />
               
              </div>
            </div>
            </div>
          </div>
        </div>
      </div>

      {/* Mitigation Section */}
      <div className="bg-white h-[450px] rounded-lg overflow-hidden">
        <div className="grid grid-cols-12  rounded shadow-xl gap-4">
          {/* Bar Chart Section */}
          <div className="col-span-8 border-r-2 h-[420px] rounde shadow-lg">
            <Chart
              chartType="ColumnChart"
              width="100%"
              height="100%"
              data={mitigationData}
              options={mitigationBarOptions}
            />
          </div>
          
          {/* Right Side Section */}
          <div className="col-span-4 flex flex-col h-[400px]">
            {/* Stats Card */}

            <div className= "bg-white  rounded shadow-xl p-4">
          <div className="relative w-[100x]  h-24 bg-purple-100 rounded-lg p-4 flex flex-col justify-between border border-green-200">
      {/* Toggle visibility button */}
      <button 
        onClick={() => setIsVisible(!isVisible)}
        className="absolute top-3 right-3 text-gray-400 hover:text-gray-600 transition-colors"
      >
        {isVisible ? (
          <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
            <circle cx="12" cy="12" r="3"/>
          </svg>
        ) : (
          <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"/>
            <line x1="1" y1="1" x2="23" y2="23"/>
          </svg>
        )}
      </button>

      {/* Main content */}
      <div className="flex flex-col">
        {/* Amount */}
        <div className="text-2xl font-bold text-purple-700 mb-1">
          250000000৳
        </div>
        
        {/* Description */}
        <div className="text-sm text-gray-600 leading-tight mb-3">
          Total Mitigation projects with <br />
          domestic finance
        </div>
      </div>

      {/* View Details link */}
      <div className="flex justify-end">
        <button className="text-sm text-blue-600 hover:text-blue-800 font-medium transition-colors">
          View Details
        </button>
      </div>
    </div>
            
            {/* Pie Chart Section */}
            <div className="flex-1 p-4 bg-white">
              <div className="mb-2">
                <h4 className="text-sm font-medium text-purple-600 mb-1">Percentage Of Total Project Funds:</h4>
                <div className="flex items-center gap-4 text-xs mb-3">
                  <div className="flex items-center gap-1">
                    <div className="w-3 h-3 bg-purple-600 rounded-sm"></div>
                    <span>Mitigation Fund</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <div className="w-3 h-3 bg-purple-300 rounded-sm"></div>
                    <span>Total Fund</span>
                  </div>
                </div>
              </div>
              <div className="relative">
                <Chart
                  chartType="PieChart"
                  width="100%"
                  height="200px"
                  data={mitigationPieData}
                  options={mitigationPieOptions}
                />
             
              </div>
            </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ClimateFinanceDashboard;