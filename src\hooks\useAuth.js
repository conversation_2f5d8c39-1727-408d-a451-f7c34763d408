import { useDispatch, useSelector } from "react-redux";
import { useEffect } from "react";
import {
  loginStart,
  loginSuccess,
  loginFailure,
  logout as logoutAction,
  loadUserFromStorage,
} from "../store/slices/authSlice";
import { useLoginMutation, useLogoutApiMutation } from "../store/api/commonApi";

export const useAuth = () => {
  const dispatch = useDispatch();
  const { user, token, isAuthenticated, isLoading, role } = useSelector(
    (state) => state.auth
  );

  const [loginMutation] = useLoginMutation();
  const [logoutMutation] = useLogoutApiMutation();

  useEffect(() => {
    dispatch(loadUserFromStorage());
  }, [dispatch]);

  const login = async (credentials) => {
    try {
      dispatch(loginStart());

      const response = await loginMutation(credentials).unwrap();

      dispatch(
        loginSuccess({
          access: response.access,
          refresh: response.refresh,
          user: response.user,
        })
      );

      return { success: true, data: response };
    } catch (error) {
      dispatch(loginFailure());
      return {
        success: false,
        error: error?.data?.detail || error?.error || "Login failed",
      };
    }
  };

  const logout = async () => {
    try {
      if (isAuthenticated && token) {
        await logoutMutation().unwrap();
      }
    } catch (error) {
      console.error("Logout API error:", error);
    } finally {
      dispatch(logoutAction());
    }
  };

  return {
    user,
    token,
    isAuthenticated,
    isLoading,
    role,
    login,
    logout,
    isAdmin: role === "admin",
  };
};
