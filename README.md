# ADB Frontend - React + Vite

This is a modern React application built with Vite, featuring a comprehensive form system using Formik and Yup validation.

## Features

- ⚡ **Vite** - Fast build tool and development server
- ⚛️ **React 19** - Latest React with modern features
- 🎨 **Tailwind CSS** - Utility-first CSS framework
- 📝 **Formik** - Build forms without tears
- ✅ **Yup** - Schema validation
- 🎯 **React Select** - Flexible select input control
- 🔄 **Swiper** - Modern touch slider
- 🎭 **Iconify** - Thousands of icons

## Form Components

This project includes a comprehensive set of reusable form components built with Formik and Yup:

### Available Components

#### 1. Form Wrapper
```jsx
import { Form } from './components';

<Form
  initialValues={initialValues}
  validationSchema={validationSchema}
  onSubmit={handleSubmit}
  submitText="Submit"
  showReset={true}
>
  {/* Form fields go here */}
</Form>
```

#### 2. Input Field
```jsx
import { InputField } from './components';

<InputField
  name="email"
  type="email"
  label="Email Address"
  placeholder="Enter your email"
  required
/>
```

**Props:**
- `name` (string, required) - Field name for Formik
- `type` (string) - Input type (text, email, password, number, tel, etc.)
- `label` (string) - Field label
- `placeholder` (string) - Placeholder text
- `required` (boolean) - Show required asterisk
- `disabled` (boolean) - Disable the field
- `className` (string) - Additional CSS classes

#### 3. Text Area
```jsx
import { TextArea } from './components';

<TextArea
  name="description"
  label="Description"
  placeholder="Enter description..."
  rows={4}
  resize={false}
/>
```

**Props:**
- `name` (string, required) - Field name for Formik
- `label` (string) - Field label
- `placeholder` (string) - Placeholder text
- `rows` (number) - Number of rows (default: 4)
- `resize` (boolean) - Allow resize (default: true)
- `required` (boolean) - Show required asterisk
- `disabled` (boolean) - Disable the field

#### 4. Select Dropdown
```jsx
import { Select } from './components';

// Single select
<Select
  name="country"
  label="Country"
  options={countryOptions}
  placeholder="Select country"
  isSearchable={true}
  isClearable={true}
/>

// Multi-select
<Select
  name="skills"
  label="Skills"
  options={skillOptions}
  placeholder="Select skills"
  isMulti={true}
  isSearchable={true}
/>
```

**Props:**
- `name` (string, required) - Field name for Formik
- `options` (array, required) - Array of {value, label} objects
- `label` (string) - Field label
- `placeholder` (string) - Placeholder text
- `isMulti` (boolean) - Enable multi-select (default: false)
- `isSearchable` (boolean) - Enable search (default: true)
- `isClearable` (boolean) - Enable clear button (default: true)
- `isDisabled` (boolean) - Disable the field
- `required` (boolean) - Show required asterisk

#### 5. Checkbox
```jsx
import { Checkbox } from './components';

<Checkbox
  name="newsletter"
  label="Subscribe to newsletter"
/>
```

**Props:**
- `name` (string, required) - Field name for Formik
- `label` (string) - Checkbox label
- `disabled` (boolean) - Disable the checkbox

#### 6. Radio Group
```jsx
import { RadioGroup } from './components';

<RadioGroup
  name="gender"
  label="Gender"
  options={genderOptions}
  inline={true}
  required
/>
```

**Props:**
- `name` (string, required) - Field name for Formik
- `options` (array, required) - Array of {value, label} objects
- `label` (string) - Group label
- `inline` (boolean) - Display options inline (default: false)
- `required` (boolean) - Show required asterisk
- `disabled` (boolean) - Disable all options

#### 7. File Upload
```jsx
import { FileUpload } from './components';

<FileUpload
  name="resume"
  label="Resume"
  accept=".pdf,.doc,.docx"
  multiple={false}
  maxSize={5 * 1024 * 1024} // 5MB
/>
```

**Props:**
- `name` (string, required) - Field name for Formik
- `label` (string) - Field label
- `accept` (string) - Accepted file types
- `multiple` (boolean) - Allow multiple files (default: false)
- `maxSize` (number) - Maximum file size in bytes (default: 5MB)
- `required` (boolean) - Show required asterisk
- `disabled` (boolean) - Disable the field

### Validation with Yup

```jsx
import * as Yup from 'yup';

const validationSchema = Yup.object({
  email: Yup.string()
    .email('Invalid email address')
    .required('Email is required'),

  age: Yup.number()
    .min(18, 'Must be at least 18')
    .required('Age is required'),

  country: Yup.object()
    .nullable()
    .required('Please select a country'),

  skills: Yup.array()
    .min(1, 'Select at least one skill'),

  terms: Yup.boolean()
    .oneOf([true], 'You must accept terms'),
});
```

### Complete Form Example

```jsx
import { Form, InputField, Select, TextArea, Checkbox } from './components';
import * as Yup from 'yup';

const MyForm = () => {
  const initialValues = {
    name: '',
    email: '',
    country: null,
    message: '',
    newsletter: false,
  };

  const validationSchema = Yup.object({
    name: Yup.string().required('Name is required'),
    email: Yup.string().email('Invalid email').required('Email is required'),
    country: Yup.object().nullable().required('Please select a country'),
  });

  const handleSubmit = (values, { setSubmitting }) => {
    console.log(values);
    setSubmitting(false);
  };

  const countryOptions = [
    { value: 'bd', label: 'Bangladesh' },
    { value: 'in', label: 'India' },
  ];

  return (
    <Form
      initialValues={initialValues}
      validationSchema={validationSchema}
      onSubmit={handleSubmit}
    >
      <InputField name="name" label="Name" required />
      <InputField name="email" type="email" label="Email" required />
      <Select name="country" label="Country" options={countryOptions} required />
      <TextArea name="message" label="Message" />
      <Checkbox name="newsletter" label="Subscribe to newsletter" />
    </Form>
  );
};
```

## Getting Started

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Start development server:**
   ```bash
   npm run dev
   ```

3. **Build for production:**
   ```bash
   npm run build
   ```

## Dependencies

### Core
- `react` - React library
- `react-dom` - React DOM renderer
- `vite` - Build tool

### Forms
- `formik` - Form library
- `yup` - Schema validation
- `react-select` - Select component

### UI
- `tailwindcss` - CSS framework
- `swiper` - Touch slider
- `@iconify/react` - Icon library

### State Management
- `@reduxjs/toolkit` - Redux toolkit
- `react-redux` - React Redux bindings
- `@tanstack/react-query` - Data fetching

### Routing
- `react-router-dom` - React router
