import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useSelector } from 'react-redux';

// Custom hook for GET requests with TanStack Query
export const useTanstackQuery = ({
  queryKey,
  endpoint,
  params = {},
  enabled = true,
  staleTime = 5 * 60 * 1000, // 5 minutes
  cacheTime = 10 * 60 * 1000, // 10 minutes
}) => {
  const token = useSelector((state) => state.auth.token);
  
  const fetchData = async () => {
    const baseUrl = import.meta.env.VITE_API_BASE_URL;
    
    // Build URL with params
    const url = new URL(`${baseUrl}/${endpoint}`);
    Object.keys(params).forEach(key => {
      if (params[key] !== undefined && params[key] !== null && params[key] !== '') {
        url.searchParams.append(key, params[key]);
      }
    });
    
    const headers = {
      'Content-Type': 'application/json',
    };
    
    // Add authorization header if token exists
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }
    
    const response = await fetch(url.toString(), {
      method: 'GET',
      headers,
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    return response.json();
  };
  
  return useQuery({
    queryKey: [queryKey, endpoint, params],
    queryFn: fetchData,
    enabled: enabled && !!endpoint,
    staleTime,
    cacheTime,
    retry: (failureCount, error) => {
      // Don't retry on 401 or 403 errors
      if (error?.status === 401 || error?.status === 403) {
        return false;
      }
      return failureCount < 3;
    },
  });
};

// Custom hook for mutations with TanStack Query
export const useTanstackMutation = ({
  onSuccess,
  onError,
  invalidateQueries = [],
}) => {
  const queryClient = useQueryClient();
  const token = useSelector((state) => state.auth.token);
  
  const mutationFn = async ({ endpoint, method = 'POST', body, isFormData = false }) => {
    const baseUrl = import.meta.env.VITE_API_BASE_URL;
    const url = `${baseUrl}/${endpoint}`;
    
    const headers = {};
    
    // Add authorization header if token exists
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }
    
    // Set content type if not form data
    if (!isFormData) {
      headers['Content-Type'] = 'application/json';
    }
    
    const config = {
      method,
      headers,
    };
    
    // Add body if provided
    if (body) {
      config.body = isFormData ? body : JSON.stringify(body);
    }
    
    const response = await fetch(url, config);
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    return response.json();
  };
  
  return useMutation({
    mutationFn,
    onSuccess: (data, variables, context) => {
      // Invalidate specified queries
      if (invalidateQueries.length > 0) {
        invalidateQueries.forEach(queryKey => {
          queryClient.invalidateQueries({ queryKey: [queryKey] });
        });
      }
      
      // Call custom onSuccess if provided
      if (onSuccess) {
        onSuccess(data, variables, context);
      }
    },
    onError: (error, variables, context) => {
      // Call custom onError if provided
      if (onError) {
        onError(error, variables, context);
      }
    },
  });
};

// Hook to invalidate queries manually
export const useInvalidateQueries = () => {
  const queryClient = useQueryClient();
  
  const invalidateQuery = (queryKey) => {
    queryClient.invalidateQueries({ queryKey: [queryKey] });
  };
  
  const invalidateAllQueries = () => {
    queryClient.invalidateQueries();
  };
  
  return {
    invalidateQuery,
    invalidateAllQueries,
  };
};
