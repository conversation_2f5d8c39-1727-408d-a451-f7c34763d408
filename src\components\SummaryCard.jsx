import { useNavigate } from "react-router-dom";

const SummaryCard = ({ title, value, description, link }) => {
  const navigate = useNavigate();

  const handleClick = () => {
    if (link) {
      navigate(link);
    }
  };

  return (
    <div
      className={`p-4 rounded-lg flex flex-col justify-between h-full ${
        link ? "cursor-pointer hover:shadow-lg transition-shadow duration-200" : ""
      }`}
      style={{
        backgroundColor: "#EDFFEA",
        border: "1px solid #C2F5B9",
        boxShadow: "0px 0px 12.75px 3.19px rgba(0, 0, 0, 0.08)",
      }}
      onClick={link ? handleClick : undefined}
    >
      <div className="flex items-center mb-2">
        <div className="h-6 w-1 bg-primary-500 rounded-full mr-2"></div>
        <h3 className="text-gray-800 font-semibold text-base">{title}</h3>
      </div>
      <p className="text-2xl font-bold text-gray-700 mb-1">{value}</p>
      <p className="text-sm text-gray-600">{description}</p>
    </div>
  );
};

export default SummaryCard;
