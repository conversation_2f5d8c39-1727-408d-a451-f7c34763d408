import { Text } from "../../components";
import PageHeader from "../../components/PageHeader";

const Policies = () => {
  const breadcrumbs = [
    { label: "Home", path: "/" },
    { label: "Policy & laws" },
  ];

  const documentLinks = [
    {
      url: "https://drive.google.com/file/d/1Xpg973NojABv5HXt-SOJUvf0NbzWpFG/view",
      title: "Policy Document 1"
    },
    {
      url: "https://drive.google.com/file/d/1Abc123DefGhi456JklMno789PqrStu012/view",
      title: "Policy Document 2"
    },
    {
      url: "https://drive.google.com/file/d/1Xyz987VwxYza654BcdEfg321HijKlm098/view",
      title: "Policy Document 3"
    },
  ];

  return (
    <div className="min-h-screen bg-white">
      {/* Mobile Header - Only shows on mobile */}
      <div className="block md:hidden px-4 py-4 bg-breadcrumb-bg">
        {/* Mobile Breadcrumbs */}
        <nav className="mb-3">
          <div className="flex items-center space-x-1 text-xs text-gray-600">
            <a href="/" className="hover:text-gray-800 transition-colors">Home</a>
            <span>/</span>
            <span className="text-gray-800 font-medium">Policy & laws</span>
          </div>
        </nav>
        
        {/* Mobile Title */}
        <div className="mb-4">
          <h1 className="text-lg sm:text-xl font-bold text-gray-900 leading-tight">
            Policies & laws
          </h1>
        </div>
        
        {/* Mobile Contact Button */}
        {/* <button 
          onClick={() => console.log("Contact board clicked")}
          className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg font-medium text-sm transition-colors duration-200 w-full sm:w-auto"
        >
          Contact Board
        </button> */}
      </div>

      {/* Desktop Header - Only shows on desktop */}
      <div className="hidden md:block">
        <PageHeader
          title="Policies & laws"
          breadcrumbs={breadcrumbs}
          buttonText="Contact Board"
          buttonOnClick={() => console.log("Contact board clicked")}
        />
      </div>

      <div 
        className="max-w-4xl mx-auto px-3 sm:px-4 lg:px-4 xl:px-6 py-4 sm:py-6 lg:py-8 xl:py-12 rounded-md mt-3 sm:mt-4 lg:mt-6 xl:mt-9"
        style={{ boxShadow: '0px 0px 12px 0px #00000029' }}
      >
        <div className="text-center mb-4 sm:mb-6 lg:mb-8">
          <Text className="text-gray-700 text-xs sm:text-sm lg:text-base xl:text-lg leading-relaxed px-1 sm:px-2">
            The Organization Will Maintain The Confidentiality Of Any Data Or Information. You Can Browse
            <span className="hidden sm:inline">
              <br />
            </span>
            <span className="sm:hidden"> </span>
            This Website Without Providing Any Personal Information.
          </Text>
        </div>

        <div className="space-y-3 sm:space-y-4 lg:space-y-6">
          {documentLinks.map((doc, index) => (
            <div key={index} className="text-center px-1 sm:px-2">
              <div className="flex items-start justify-center sm:inline-flex sm:items-center sm:justify-center bg-gray-50 sm:bg-transparent p-3 sm:p-0 rounded-lg sm:rounded-none">
                <svg
                  className="w-4 h-4 sm:w-5 sm:h-5 lg:w-5 lg:h-5 text-blue-600 mr-2 sm:mr-3 lg:mr-3 flex-shrink-0 mt-0.5 sm:mt-0"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                  />
                </svg>
                <div className="flex-1 min-w-0">
                  {/* Mobile: Show document title */}
                  <div className="sm:hidden mb-2">
                    <span className="text-gray-800 font-medium text-sm">
                      {doc.title}
                    </span>
                  </div>
                  <a
                    href={doc.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:text-blue-800 underline hover:no-underline text-xs sm:text-sm lg:text-sm leading-tight block sm:inline word-break"
                    style={{ wordBreak: 'break-all' }}
                  >
                    <span className="sm:hidden">
                      {/* Mobile: Show shortened URL */}
                      View Document
                    </span>
                    <span className="hidden sm:inline">
                      {/* Desktop: Show full URL */}
                      {doc.url}
                    </span>
                  </a>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Mobile: Additional info section */}
        <div className="mt-6 sm:mt-8 lg:hidden">
          <div className="bg-blue-50 p-4 rounded-lg">
            <div className="flex items-start space-x-2">
              <svg
                className="w-5 h-5 text-blue-600 flex-shrink-0 mt-0.5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
              <div>
                <h3 className="text-sm font-medium text-blue-800 mb-1">
                  Document Access
                </h3>
                <p className="text-xs text-blue-700">
                  Tap "View Document" to open policy documents in a new tab. Documents are hosted on Google Drive for secure access.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Policies;