import { Text } from "../../components";

// Data for NAP Sector table
const napSectorData = [
  {
    id: 1,
    napSector: "Water resources",
    noOfProjects: "####",
    externalFundAllocated: "############",
    fundSpent: "############",
    percentFundSpent: "####"
  },
  {
    id: 2,
    napSector: "Disaster, social safety and security",
    noOfProjects: "####",
    externalFundAllocated: "############",
    fundSpent: "############",
    percentFundSpent: "####"
  },
  {
    id: 3,
    napSector: "Agriculture",
    noOfProjects: "####",
    externalFundAllocated: "############",
    fundSpent: "############",
    percentFundSpent: "####"
  },
  {
    id: 4,
    napSector: "Fisheries, aquaculture and livestock",
    noOfProjects: "####",
    externalFundAllocated: "############",
    fundSpent: "############",
    percentFundSpent: "####"
  },
  {
    id: 5,
    napSector: "Urban areas",
    noOfProjects: "####",
    externalFundAllocated: "############",
    fundSpent: "############",
    percentFundSpent: "####"
  },
  {
    id: 6,
    napSector: "Ecosystems, wetlands and biodiversity",
    noOfProjects: "####",
    externalFundAllocated: "############",
    fundSpent: "############",
    percentFundSpent: "####"
  },
  {
    id: 7,
    napSector: "Policies and institutions",
    noOfProjects: "####",
    externalFundAllocated: "############",
    fundSpent: "############",
    percentFundSpent: "####"
  },
  {
    id: 8,
    napSector: "Capacity development, research and innovation",
    noOfProjects: "####",
    externalFundAllocated: "############",
    fundSpent: "############",
    percentFundSpent: "####"
  }
];

const NapSectorTable = ({ 
  title = "External Fund Allocation Of The Ongoing Projects By NAP Sector",
  showSeeAllButton = true,
  data = napSectorData,
  onSeeAllClick = () => console.log("See All clicked"),
  className = ""
}) => {
  return (
    <div className={`max-w-container mx-auto px-4 py-8 ${className}`}>
      <div className="flex justify-between items-center mb-6">
        <Text
          variant= "normal"
          weight="normal"
          as="h3"
          className="text-primary-700"
        >
          {title}
        </Text>
        {showSeeAllButton && (
          <button 
            onClick={onSeeAllClick}
            className="text-primary-500 hover:text-primary-700 text-sm font-medium transition-colors duration-200"
          >
            See All
          </button>
        )}
      </div>

      <div className="overflow-x-auto rounded-lg shadow-md border border-gray-100">
        <table className="min-w-full">
          <thead className="bg-gray-100">
            <tr>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
              >
                #
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
              >
                NAP Sector
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
              >
                No. of projects
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
              >
                External Fund allocated
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
              >
                Fund spent
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
              >
                % of fund spent
              </th>
            </tr>
          </thead>
          <tbody className="bg-white">
            {data.map((item, index) => (
              <tr
                key={item.id}
                className={`${
                  index % 2 === 0 ? "bg-light-green-bg" : "bg-white"
                }`}
              >
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  {item.id}
                </td>
                <td className="px-6 py-4 text-sm text-gray-700">
                  {item.napSector}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                  {item.noOfProjects}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                  {item.externalFundAllocated}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                  {item.fundSpent}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                  {item.percentFundSpent}
                </td>
              </tr>
            ))}
            {/* Total row */}
            <tr className="bg-green-100 font-medium">
              <td className="px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900">
                
              </td>
              <td className="px-6 py-4 text-sm font-bold text-gray-900">
                Total
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900">
                ####
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900">
                ############
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900">
                ############
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900">
                ####
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default NapSectorTable;