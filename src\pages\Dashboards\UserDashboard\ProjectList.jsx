import React from "react";
import { useNavigate } from "react-router-dom";

const ProjectListTable = () => {
  // Mock Data
  const projects = [
    {
      id: 1,
      name: "Project A",
      status: "In progress",
      startEndYear: "2022-2027",
      actions: [
        { type: "Project Info", color: "bg-green-600" },
        { type: "Project Funding", color: "bg-green-600" },
        { type: "Project Performance", color: "bg-green-600" },
        { type: "Policy Knowledge Capacity", color: "bg-green-600" },
      ],
    },
    {
      id: 2,
      name: "Project B",
      status: "Complete",
      startEndYear: "2020-2025",
      actions: [
        { type: "Project Info", color: "bg-green-600" },
        { type: "Project Funding", color: "bg-green-600" },
        { type: "Project Performance", color: "bg-green-600" },
        { type: "Policy Knowledge Capacity", color: "bg-green-600" },
      ],
    },
    {
      id: 3,
      name: "Project C",
      status: "In progress",
      startEndYear: "2021-2026",
      actions: [
        { type: "Project Info", color: "bg-green-600" },
        { type: "Project Funding", color: "bg-green-600" },
        { type: "Project Performance", color: "bg-green-600" },
        { type: "Policy Knowledge Capacity", color: "bg-green-600" },
      ],
    },
    {
      id: 4,
      name: "Project D",
      status: "Complete",
      startEndYear: "2022-2027",
      actions: [
        { type: "Project Info", color: "bg-green-600" },
        { type: "Project Funding", color: "bg-green-600" },
        { type: "Project Performance", color: "bg-green-600" },
        { type: "Policy Knowledge Capacity", color: "bg-green-600" },
      ],
    },
  ];

  const getStatusColor = (status) => {
    switch (status.toLowerCase()) {
      case "complete":
        return "bg-green-100 text-green-700 border-green-200";
      case "in progress":
        return "bg-orange-100 text-orange-700 border-orange-200";
      default:
        return "bg-gray-100 text-gray-700 border-gray-200";
    }
  };

  const navigate = useNavigate();

  const handleAddProject = () => {
    navigate('/project-information')
  };

  return (
    <div className="max-w-container mx-auto mt-5 mb-5">
      {/* Add Project Button */}
      <div className="flex justify-end mb-4">
        <button
          onClick={handleAddProject}
          className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
        >
          Add Project
        </button>
      </div>

      {/* Table */}
      <div className="overflow-x-auto rounded-lg shadow-md border border-gray-100">
        <table className="min-w-full">
          <thead className="bg-gray-100">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                #
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                Project Name
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                Status
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                Started & End Year
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {projects.length > 0 ? (
              projects.map((project, index) => (
                <tr key={project.id}>
                  <td className="px-6 py-4 text-sm font-medium text-gray-900">
                    {project.id}
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-700">
                    {project.name}
                  </td>
                  <td className="px-6 py-4">
                    <span
                      className={`inline-flex px-3 py-1 rounded-full text-xs font-medium border ${getStatusColor(
                        project.status
                      )}`}
                    >
                      {project.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-700">
                    {project.startEndYear}
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex flex-wrap gap-2">
                      {project.actions.map((action, index) => (
                        <button
                          key={index}
                          className={`${action.color} hover:opacity-90 text-white px-3 py-1 rounded text-xs font-medium transition-opacity flex items-center gap-1`}
                        >
                          {action.type}
                          <span className="text-xs">✎</span>
                        </button>
                      ))}
                    </div>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td
                  colSpan="5"
                  className="px-6 py-4 text-sm text-center text-gray-700"
                >
                  No Data Found
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default ProjectListTable;
