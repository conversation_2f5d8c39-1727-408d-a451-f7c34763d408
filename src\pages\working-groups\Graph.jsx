import { Chart } from "react-google-charts";

// Data for the bar graph (converted to Google Charts format)
const barData = [
  ["Year", "Value"],
  ["2015", 350],
  ["2016", 200],
  ["2017", 330],
  ["2018", 450],
  ["2019", 380],
  ["2020", 360],
  ["2021", 230],
  ["2022", 420],
  ["2023", 390],
  ["2024", 320],
];

const barOptions = {
  backgroundColor: 'transparent',
  chartArea: {
    left: 60,
    top: 20,
    width: '85%',
    height: '75%'
  },
  colors: ["#3B82F6"],
  hAxis: {
    title: '',
    titleTextStyle: { color: '#6B7280', fontSize: 12 },
    textStyle: { color: '#6B7280', fontSize: 12 },
    gridlines: { color: 'transparent' },
    baselineColor: 'transparent'
  },
  vAxis: {
    title: '',
    titleTextStyle: { color: '#6B7280', fontSize: 12 },
    textStyle: { color: '#6B7280', fontSize: 12 },
    gridlines: { color: '#E5E7EB', lineDashStyle: [3, 3] },
    baselineColor: '#E5E7EB',
    minValue: 0,
    maxValue: 500
  },
  legend: { position: 'none' },
  bar: { groupWidth: '60%' }
};

// Data for the 3D pie chart
const pieData = [
  ["Fund Type", "Percentage"],
  ["Total Fund", 75],
  ["Mitigation Fund", 25]
];

const pieOptions = {
  is3D: true,
  pieStartAngle: 0,
  sliceVisibilityThreshold: 0.02,
  legend: {
    position: "none",
  },
  colors: ["#93C5FD", "#3B82F6"],
  backgroundColor: 'transparent',
  chartArea: {
    left: 10,
    top: 10,
    width: '100%',
    height: '100%'
  },
  pieSliceText: 'percentage',
  pieSliceTextStyle: {
    color: 'white',
    fontSize: 12,
    bold: true
  }
};

// Graph and Info Section component
const Graph = () => {
  return (
    <div className="w-full p-2">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Left side - Bar Chart */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className='bg-gray-100 w-full p-2'>
            Number of total mitigation projects with domestic finance:
          </div>
          <div className="text-xs text-gray-600 mb-2 mt-6 p-2">
            In million<br />USD
          </div>
          
          <div className="h-80 p-2">
            <Chart
              chartType="ColumnChart"
              data={barData}
              options={barOptions}
              width="100%"
              height="100%"
            />
          </div>
        </div>

        {/* Right side - Info Cards */}
        <div className="space-y-6">
          {/* Pie Chart Section */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"> 
            
            <div className="bg-blue-50 rounded-lg shadow-sm border border-gray-200 p-2">
              <div className="flex items-start justify-between mb-4">
                <div>
                  <h3 className="text-5xl font-bold text-gray-600 mb-1">25</h3>
                  <p className="text-sm text-gray-500">Million USD</p>
                </div>
              </div>
              <div className="flex items-start justify-between">
                <div className="flex items-center">
                  <div className="h-6 w-1 bg-blue-500 rounded-full"></div>
                  <h4 className="text-lg font-semibold text-gray-700 ml-2">
                    Total Mitigation Projects With Domestic Finance
                  </h4>
                </div>
                <button className="text-blue-600 text-sm hover:underline font-medium ml-1">
                  View Details
                </button>
              </div>
            </div>

            <div className="flex items-start gap-24">
              <h4 className="text-lg font-semibold text-gray-700 mb-6 mt-8">
                Percentage Of Total Project Funds:
              </h4>
              
              {/* Legend */}
              <div className="flex flex-col gap-2 mb-6 mt-10">
                <div className="flex items-center">
                  <div className="w-4 h-4 bg-blue-500 rounded-sm mr-2"></div>
                  <span className="text-sm text-gray-600">Mitigation Fund</span>
                </div>
                <div className="flex items-center">
                  <div className="w-4 h-4 bg-blue-300 rounded-sm mr-2"></div>
                  <span className="text-sm text-gray-600">Total Fund</span>
                </div>
              </div>
            </div>

            {/* 3D Pie Chart with Google Charts */}
            <div className="flex justify-center -mt-9">
              <div className="w-80 h-64">
                <Chart
                  chartType="PieChart"
                  data={pieData}
                  options={pieOptions}
                  width="100%"
                  height="100%"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Graph;