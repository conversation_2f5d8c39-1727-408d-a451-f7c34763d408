import { Chart } from "react-google-charts";
import Text from "./Text";

// const generateRandomColor = () =>
//   "#" +
//   Array.from(
//     { length: 6 },
//     () => "0123456789ABCDEF"[Math.floor(Math.random() * 16)]
//   ).join("");

const defaultColors = ["#3B82F6", "#10B981", "#F87171", "#60A5FA"];

const Barchart = ({
  data = [],
  title = "Total Emission",
  colors = defaultColors,
  height = 300,
}) => {
  const labels = data?.[0]?.slice(1) || [];
  //   const chartColors =
  //     colors?.length === labels.length
  //       ? colors
  //       : Array.from({ length: labels.length }, generateRandomColor);

  const chartOptions = {
    isStacked: true,
    backgroundColor: "transparent",
    legend: { position: "none" },
    chartArea: { left: 40, top: 50, width: "100%", height: "80%" },
    colors: colors,
  };

  if (!Array.isArray(data) || data.length < 2) return null;

  return (
    <div>
      <div className="flex items-start justify-between">
        <Text
          as="h3"
          variant="body"
          weight="bold"
          className="text-green-600 text-base mb-4"
        >
          {title}
        </Text>
        <div className="flex flex-wrap gap-4 text-sm">
          {labels.map((label, idx) => (
            <div key={label} className="flex items-center gap-2">
              <div
                className="w-4 h-4 rounded-sm"
                style={{ backgroundColor: colors[idx] }}
              />
              <span>{label}</span>
            </div>
          ))}
        </div>
      </div>
      <div className="w-full" style={{ height }}>
        <Chart
          chartType="ColumnChart"
          width="100%"
          height="100%"
          data={data}
          options={chartOptions}
        />
      </div>
    </div>
  );
};

export default Barchart;
