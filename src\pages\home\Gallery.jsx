import React from 'react';
import { Text } from '../../components';

const Gallery = ({
  items = [],
  className = '',
  title = 'Gallery',
  subtitle,
}) => {
  return (
    <div className={`w-full px-4 py-6 ${className}`}>
      <Text variant="header" weight="bold" as="h1" className="text-primary-500 mb-4">
        {title}
      </Text>

      {subtitle && (
        <Text variant="body" className="text-gray-600 mb-4">
          {subtitle}
        </Text>
      )}

      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        <div className="flex flex-col gap-4">
          <div className="relative overflow-hidden rounded-lg">
            <img
              src="https://picsum.photos/610/450?random=1"
              alt="Gallery Large 1"
              className="w-full h-[450px] object-cover"
            />
            <div className="absolute bottom-0 right-0 w-1/2 bg-gradient-to-r from-gray-200/60 to-gray-400/60 backdrop-blur-md rounded-tl-lg px-4 py-1">
              <Text variant="caption" weight="medium" className="text-gray-800">
                P.C : ADB
              </Text>
            </div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            {['2', '3'].map((n) => (
              <div key={n} className="relative overflow-hidden rounded-lg">
                <img
                  src={`https://picsum.photos/295/348?random=${n}`}
                  alt={`Gallery Small ${n}`}
                  className="w-full h-[348px] object-cover"
                />
                <div className="absolute bottom-0 right-0 w-1/2 bg-gradient-to-r from-gray-200/60 to-gray-400/60 backdrop-blur-md rounded-tl-lg px-4 py-1">
                  <Text variant="caption" weight="medium" className="text-gray-800">
                    P.C : ADB
                  </Text>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="flex flex-col gap-4">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            {['4', '5'].map((n) => (
              <div key={n} className="relative overflow-hidden rounded-lg">
                <img
                  src={`https://picsum.photos/295/348?random=${n}`}
                  alt={`Gallery Small ${n}`}
                  className="w-full h-[348px] object-cover"
                />
                <div className="absolute bottom-0 right-0 w-1/2 bg-gradient-to-r from-gray-200/60 to-gray-400/60 backdrop-blur-md rounded-tl-lg px-4 py-1">
                  <Text variant="caption" weight="medium" className="text-gray-800">
                    P.C : ADB
                  </Text>
                </div>
              </div>
            ))}
          </div>

          <div className="relative overflow-hidden rounded-lg">
            <img
              src="https://picsum.photos/610/450?random=6"
              alt="Gallery Large 2"
              className="w-full h-[450px] object-cover"
            />
            <div className="absolute bottom-0 right-0 w-1/2 bg-gradient-to-r from-gray-200/60 to-gray-400/60 backdrop-blur-md rounded-tl-lg px-4 py-1">
              <Text variant="caption" weight="medium" className="text-gray-800">
                P.C : ADB
              </Text>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Gallery;
