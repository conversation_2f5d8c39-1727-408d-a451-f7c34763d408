import { Text } from "../../components";
import HoverableCard from "../../components/HoverableCard";
import Gallery from "./Gallery";
import Research from "./Research";
import FeedbackForm from "./FeedbackForm";
import Map from "./Map";
import heroBg from "../../assets/herobg.png";
import { motion, AnimatePresence } from "framer-motion";
import { useState, useEffect } from "react";

const Home = () => {
  // Hero slider images
  const heroImages = [
    "https://picsum.photos/600/400?random=1",
    "https://picsum.photos/600/400?random=11",
    "https://picsum.photos/600/400?random=12",
  ];

  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  // Auto-advance slider
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentImageIndex((prevIndex) =>
        prevIndex === heroImages.length - 1 ? 0 : prevIndex + 1
      );
    }, 3000);

    return () => clearInterval(interval);
  }, [heroImages.length]);

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section
        className="py-16 lg:py-32 relative overflow-visible bg-cover bg-center bg-no-repeat"
        style={{ backgroundImage: `url(${heroBg})` }}
      >
        <div className="max-w-container mx-auto w-full px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-start relative">
            {/* Left Content */}
            <div className="text-left lg:pt-8">
              <Text
                variant="header"
                weight="bold"
                as="h1"
                className="text-gray-800 mb-6"
              >
                A Partnership to Drive Climate Actions at Scale and with Urgency
              </Text>
              <Text variant="body" as="p" className="text-gray-600 max-w-lg">
                The Bangladesh Climate Development Partnership (BCDP) is a
                cross-sector, multistakeholder, and multi-year partnership to
                drive climate actions at scale and with urgency, to support
                low-carbon and climate-resilient development essential to
                achieve Bangladesh Vision.
              </Text>
            </div>

            {/* Right Hero Image Slider */}
            <div className="relative">
              <div className="absolute -top-8 lg:-top-16 bottom-16 -left-4 w-[630px] h-[250px] lg:h-[445px] z-0">
                <div className="relative w-full h-full rounded-2xl overflow-hidden shadow-2xl">
                  <AnimatePresence>
                    <motion.img
                      key={currentImageIndex}
                      src={heroImages[currentImageIndex]}
                      alt="Environmental protection and climate action"
                      className="w-full h-full object-cover absolute inset-0"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0 }}
                      transition={{
                        duration: 1.2,
                        ease: "easeInOut",
                      }}
                    />
                  </AnimatePresence>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Map Section with Statistics */}
      <Map />

      {/* News & Events Section */}
      <section className="py-16 lg:py-4 bg-white">
        <div className="max-w-container mx-auto px-4">
          <div className="flex justify-between items-center mb-12">
            <Text
              variant="header"
              weight="bold"
              as="h2"
              className="text-gray-800"
            >
              News & Events
            </Text>
            <button className="text-primary-600 hover:text-primary-700 font-medium">
              See All
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Card 1 */}
            <HoverableCard
              image="https://picsum.photos/400/300?random=2"
              title="Bangladesh Climate Development Partnership (BCDP) Launched"
              author="Staff Reporter"
              date="Jan 26, 2025"
            />

            {/* Card 2 */}
            <HoverableCard
              image="https://picsum.photos/400/300?random=3"
              title="In this section Event or news title will shown here"
              author="Host Name"
              date="dd/mm/yy"
            />

            {/* Card 3 */}
            <HoverableCard
              image="https://picsum.photos/400/300?random=4"
              title="In this section Event or news title will shown here"
              author="Host Name"
              date="dd/mm/yy"
            />
          </div>
        </div>
      </section>

      {/* Gallery Section */}
      <section className="py-16 lg:py-20 bg-white">
        <div className="max-w-container mx-auto px-4">
          <Gallery
            title="Gallery"
            subtitle=""
            items={[
              {
                id: 1,
                image: "https://picsum.photos/400/300?random=5",
                title: "Climate Action Workshop 2024",
                author: "BCDP Team",
                date: "Feb 15, 2024",
              },
              {
                id: 2,
                image: "https://picsum.photos/400/300?random=6",
                title: "Sustainable Development Conference",
                author: "Event Coordinator",
                date: "Feb 10, 2024",
              },
              {
                id: 3,
                image: "https://picsum.photos/400/300?random=7",
                title: "Community Engagement Program",
                author: "Field Team",
                date: "Feb 05, 2024",
              },
              {
                id: 4,
                image: "https://picsum.photos/400/300?random=8",
                title: "Environmental Research Initiative",
                author: "Research Team",
                date: "Jan 30, 2024",
              },
              {
                id: 5,
                image: "https://picsum.photos/400/300?random=9",
                title: "Partnership Meeting with Stakeholders",
                author: "Partnership Team",
                date: "Jan 25, 2024",
              },
              {
                id: 6,
                image: "https://picsum.photos/400/300?random=10",
                title: "Climate Resilience Training",
                author: "Training Team",
                date: "Jan 20, 2024",
              },
            ]}
          />
        </div>
      </section>

      {/* Research Section */}
      <section className="py-16 lg:py-20 bg-white">
        <div className="max-w-container mx-auto px-4">
          <Research />
        </div>
      </section>

      {/* Feedback Section */}
      <section className="py-16 lg:py-8">
        <div className="max-w-container mx-auto px-4">
          <FeedbackForm />
        </div>
      </section>
    </div>
  );
};

export default Home;
