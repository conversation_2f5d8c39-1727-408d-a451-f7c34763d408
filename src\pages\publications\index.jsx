import { Text } from "../../components";
import PageHeader from "../../components/PageHeader";
import { Icon } from "@iconify/react";

const PublicationCard = ({filename, onDownload }) => (
  <div className="bg-white border border-gray-200 rounded-lg p-4 sm:p-6 shadow-sm hover:shadow-md transition-shadow">
    <div className="flex flex-col items-center text-center">
      <div className="bg-white border-2 border-dashed border-gray-300 rounded-lg p-4 sm:p-6 mb-4 flex flex-col items-center text-center w-full max-w-36 h-24 sm:h-32">
        <div className="mb-2">
          <div className="flex items-center">
            <img
              src="/assets/pdf.png"
              alt="PDF Icon"
              className="w-8 h-8 sm:w-12 sm:h-12"
            />
          </div>
        </div>

        {/* Publication Info - Two Lines */}
        <div className="flex-grow">
          <p className="text-xs sm:text-sm text-gray-600">Publications</p>
          <p className="text-xs sm:text-sm text-gray-600">{filename}</p>
        </div>
      </div>

      {/* Download Button */}
      <button
        onClick={onDownload}
        className="w-full bg-green-700 text-white py-2 px-3 sm:px-4 rounded-lg hover:bg-green-800 transition-colors flex items-center justify-center gap-2 text-sm"
      >
        <Icon icon="tabler:download" className="w-4 h-4" />
        Download
      </button>
    </div>
  </div>
);

const Publications = () => {
  const breadcrumbs = [
    { label: "Home", path: "/" },
    { label: "Research and Publications" },
  ];

  // Sample publications data
  const publications = [
    {
      id: 1,
      filename: "Publications name.pdf",
    },
    {
      id: 2,
      filename: "Publications name.pdf",
    },
    {
      id: 3,
      filename: "Publications name.pdf",
    },
    {
      id: 4,
      filename: "Publications name.pdf",
    },
    {
      id: 5,
      filename: "Publications name.pdf",
    },
    {
      id: 6,
      filename: "Publications name.pdf",
    },
  ];

  const handleDownload = (publicationId) => {
    console.log(`Downloading publication ${publicationId}`);
    // Implement actual download logic here
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Mobile Header - Only shows on mobile */}
      <div className="block md:hidden px-3 py-4 bg-breadcrumb-bg">
        {/* Mobile Breadcrumbs */}
        <nav className="mb-3">
          <div className="flex items-center space-x-1 text-xs text-gray-600">
            <a href="/" className="hover:text-gray-800 transition-colors">
              Home
            </a>
            <span>/</span>
            <span className="text-gray-800 font-medium">Publications</span>
          </div>
        </nav>

        {/* Mobile Title */}
        <div className="mb-4">
          <h1 className="text-lg font-bold text-gray-900 leading-tight">
            Publications
          </h1>
        </div>
      </div>

      {/* Desktop Header - Only shows on desktop */}
      <div className="hidden md:block [&_nav_a]:mx-1 sm:[&_nav_a]:mx-2 md:[&_nav_a]:mx-3 [&_nav_span]:mx-1 sm:[&_nav_span]:mx-2 md:[&_nav_span]:mx-3 [&_nav>*:first-child]:ml-0">
        <div>
          <PageHeader
            title="Publications"
            breadcrumbs={breadcrumbs}
            buttonText="Contact Board"
            buttonOnClick={() => console.log("Contact board clicked")}
          />
        </div>
      </div>

      <div className="max-w-6xl mx-auto px-4 sm:px-6">
        <div className="mb-8">
          {/* Solid line above */}
          <div className="w-full h-0.5 bg-green-500 mb-4 mt-12"></div>

          <Text
            variant="header"
            weight="bold"
            as="h2"
            className="text-gray-800 mb-4 text-xl sm:text-2xl lg:text-3xl"
          >
            Publications
          </Text>

          {/* Dashed line below */}
          <div className="w-full h-0.5 border-b-2 border-dashed border-green-500 mb-6"></div>
        </div>

        {/* Publications Grid */}
        <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 sm:gap-4 lg:gap-6 mb-12">
          {publications.map((publication) => (
            <PublicationCard
              key={publication.id}
              title={publication.title}
              filename={publication.filename}
              onDownload={() => handleDownload(publication.id)}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default Publications;
