import { Chart } from "react-google-charts";

// Data for NAP Sector (converted to Google Charts format)
const napSectorData = [
  [
    "Category",
    "Estimated fund requirement as per NAP",
    "Investment already made", 
    "Total investment (completed and ongoing)",
    "Financing gap"
  ],
  ["Water resources", 3000000, 2000000, 1500000, 500000],
  ["Disaster risk reduction, early warning and climate information services", 2500000, 1800000, 1200000, 700000],
  ["Agriculture", 2200000, 1500000, 1000000, 700000],
  ["Fisheries, livestock and poultry sectors", 1800000, 1200000, 900000, 600000],
  ["Urban areas", 2800000, 2000000, 1400000, 800000],
  ["Ecosystem and biodiversity including coastal and marine ecosystems", 2400000, 1600000, 1100000, 800000],
  ["Policies and institutions", 1500000, 1000000, 700000, 500000],
  ["Capacity development, awareness and knowledge management", 2600000, 1800000, 1200000, 800000]
];

// Data for NDC Sector (converted to Google Charts format)
const ndcSectorData = [
  [
    "Category",
    "Estimated fund requirement as per NAP",
    "Investment already made",
    "Total investment (completed and ongoing)", 
    "Financing gap"
  ],
  ["Energy (Power)", 2200000, 1500000, 1000000, 700000],
  ["Transport", 2000000, 1300000, 900000, 700000],
  ["Industrial Processes and Product Use (IPPU)", 1800000, 1200000, 800000, 600000],
  ["Agriculture, Forestry and Other Land Use (AFOLU)", 2500000, 1700000, 1200000, 800000],
  ["Waste", 1500000, 1000000, 700000, 500000]
];

const colors = ["#3b4da6", "#6b7ed6", "#a4b9ff", "#4a9eff"];

const chartOptions = {
  backgroundColor: 'transparent',
  isStacked: true,
  chartArea: {
    left: 80,
    top: 50,
    width: '70%',
    height: '65%'
  },
  colors: colors,
  hAxis: {
    title: '',
    titleTextStyle: { color: '#6B7280', fontSize: 12 },
    textStyle: { color: '#6B7280', fontSize: 10 },
    slantedText: true,
    slantedTextAngle: 45,
    maxTextLines: 3,
    gridlines: { color: 'transparent' },
    baselineColor: 'transparent'
  },
  vAxis: {
    title: 'Value (in million USD)',
    titleTextStyle: { color: '#6B7280', fontSize: 12 },
    textStyle: { color: '#6B7280', fontSize: 12 },
    gridlines: { color: '#E5E7EB', lineDashStyle: [3, 3] },
    baselineColor: '#E5E7EB',
    minValue: 0,
    maxValue: 3500000,
    format: 'short'
  },
  legend: { position: 'none' },
  bar: { groupWidth: '70%' },
  tooltip: {
    isHtml: true,
    trigger: 'focus'
  }
};

const InlineLegend = () => (
  <div className="bg-white p-3 pt-7 min-w-max">
    <div className="flex flex-col gap-2">
      <div className="flex items-center">
        <div className="w-3 h-3 mr-2" style={{ backgroundColor: "#3b4da6" }}></div>
        <span className="text-xs text-gray-700 whitespace-nowrap">Estimated fund requirement as per NAP</span>
      </div>
      <div className="flex items-center">
        <div className="w-3 h-3 mr-2" style={{ backgroundColor: "#6b7ed6" }}></div>
        <span className="text-xs text-gray-700 whitespace-nowrap">Investment already made</span>
      </div>
      <div className="flex items-center">
        <div className="w-3 h-3 mr-2" style={{ backgroundColor: "#a4b9ff" }}></div>
        <span className="text-xs text-gray-700 whitespace-nowrap">Total investment (completed and ongoing)</span>
      </div>
      <div className="flex items-center">
        <div className="w-3 h-3 mr-2" style={{ backgroundColor: "#4a9eff" }}></div>
        <span className="text-xs text-gray-700 whitespace-nowrap">Financing gap</span>
      </div>
    </div>
  </div>
);

const ChartSection = ({ title, data, titleColor }) => (
  <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
    <div className="flex justify-between items-start mb-4">
      <div className="flex-1">
        <h2 className="text-xl font-bold text-center" style={{ color: titleColor }}>{title}</h2>
      </div>
      <div className="ml-4">
        <InlineLegend />
      </div>
    </div>
    
    <div className="h-96">
      <Chart
        chartType="ColumnChart"
        data={data}
        options={chartOptions}
        width="100%"
        height="100%"
      />
    </div>
  </div>
);

const ClimateFundDashboard = () => {
  return (
    <div className="w-full">
      <div className="max-w-7xl mx-auto">
        <h1 className="text-xl font-bold text-center mb-8 text-green-600">
          Climate Fund Investment By Sector (Combined By Domestic And External Finance)
        </h1>
        
        <div className="grid grid-cols-1 xl:grid-cols-2 gap-8">
          <ChartSection 
            title="NAP Sector" 
            data={napSectorData}
            titleColor="#10b981"
          />
          
          <ChartSection 
            title="NDC Sector" 
            data={ndcSectorData}
            titleColor="#10b981"
          />
        </div>
      </div>
    </div>
  );
};

export default ClimateFundDashboard;