import { Text } from "../../../components";
import PageHeader from "../../../components/PageHeader";
import NapSectorTable from "../NapSectorTable"; // Import the new component

// Data for the board composition table (unchanged)
const boardComposition = [
  {
    id: 1,
    position: "Chair",
    details:
      "Honorable Principal Secretary to the Chief Advisor's Office/Prime Minister's Office",
  },
  {
    id: 2,
    position: "Member",
    details: "Secretary, Planning Division, Ministry of Planning",
  },
  {
    id: 3,
    position: "Member",
    details: "Secretary, Finance Division, Ministry of Finance",
  },
  {
    id: 4,
    position: "Member",
    details: "Secretary, Economic Relations Division, Ministry of Finance",
  },
  {
    id: 5,
    position: "Member",
    details:
      "Secretary, Implementation, Monitoring and Evaluation Division, Ministry of Planning",
  },
  {
    id: 6,
    position: "Member",
    details:
      "Executive Chairman, Bangladesh Investment Development Authority (BIDA), Prime Minister's Office",
  },
  {
    id: 7,
    position: "Member",
    details: "Secretary, Ministry of Disaster Management and Relief",
  },
  {
    id: 8,
    position: "Member",
    details:
      "Secretary, Local Government Division, Ministry of Local Government, Rural Development and Co-operatives",
  },
  {
    id: 9,
    position: "Member",
    details: "Secretary, Ministry of Water Resources",
  },
  { id: 10, position: "Member", details: "Secretary, Ministry of Agriculture" },
  {
    id: 11,
    position: "Member",
    details:
      "One (1) Representative of Private Sector (nominated by the Chairperson)",
  },
  {
    id: 12,
    position: "Member",
    details: "Two (2) Representatives from Development Partners",
  },
  {
    id: 13,
    position: "Member",
    details: "Secretary, Ministry of Environment, Forest and Climate Change",
  },
];

// Data for the summary cards (unchanged)
const summaryData = [
  {
    title: "Total Projects",
    value: "1325",
    description: "Projects as of now",
  },
  {
    title: "On Going Projects",
    value: "743",
    description: "Projects as of now",
  },
  {
    title: "Total Commitments",
    value: "49,879",
    description: "Million USD",
  },
  {
    title: "Total Disbursements",
    value: "30,346",
    description: "Million USD",
  },
];

// Enhanced SummaryCard component with mobile responsiveness
const SummaryCard = ({ title, value, description }) => (
  <div className="bg-green-50 px-3 md:px-6 py-4 md:py-8 rounded-lg shadow-md border border-[#EDFFEA] flex flex-col justify-between h-full hover:shadow-lg transition-shadow duration-200">
    <div className="flex items-center mb-2 md:mb-4">
      <div className="h-3 md:h-6 w-1 bg-green-500 rounded-full mr-2 md:mr-3"></div>
      <h3 className="text-gray-800 font-semibold text-xs md:text-base">{title}</h3>
    </div>
    <p className="text-lg md:text-3xl font-bold text-gray-800 mb-1 md:mb-2">{value}</p>
    <p className="text-xs md:text-sm text-gray-600">{description}</p>
  </div>
);

const Adaption = () => {
  // Fixed breadcrumb items
  const breadcrumbs = [
    { label: "Home", path: "/" },
    { label: "Working Group 2: Climate Finance (External)" }
  ];

  const handleSeeAllClick = () => {
    console.log("See All NAP Sectors clicked");
    // Add your navigation logic here
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Mobile Header - Only shows on mobile */}
      <div className="block md:hidden px-3 py-3 bg-breadcrumb-bg">
        {/* Mobile Breadcrumbs */}
        <nav className="mb-2">
          <div className="flex items-center space-x-1 text-xs text-gray-600">
            <a href="/" className="hover:text-gray-800 transition-colors">Home</a>
            <span>/</span>
            <span className="text-gray-800 font-medium">Working Group 2</span>
          </div>
        </nav>
        
        {/* Mobile Title */}
        <div className="mb-3">
          <h1 className="text-base font-bold text-gray-900 leading-tight">
            Working Group 2: Climate Finance (External)
          </h1>
        </div>
        
        {/* Mobile Contact Button */}
        {/* <button 
          onClick={() => console.log("Contact board clicked")}
          className="bg-primary-600 hover:bg-primary-700 text-white px-3 py-2 rounded-lg font-medium text-xs transition-colors duration-200"
        >
          Contact Board
        </button> */}
      </div>

      {/* Desktop Header - Only shows on desktop */}
      <div className="hidden md:block [&_nav_a]:mx-3 [&_nav_span]:mx-3 [&_nav>*:first-child]:ml-0">
        <PageHeader 
          title={
            <>
              Working Group 2: <br />
              Climate Finance (External)
            </>
          }
          breadcrumbs={breadcrumbs}
          buttonText="Contact Board"
          buttonOnClick={() => console.log("Contact board clicked")}
        />
      </div>

      {/* Hero Section - Enhanced mobile responsiveness */}
      <div className="max-w-container mx-auto px-3 md:px-4 py-4 md:py-12">
        <div className="flex flex-col lg:flex-row gap-4 md:gap-6 lg:gap-16 items-center">
          <div className="flex-shrink-0 w-full max-w-sm lg:w-[361px] lg:h-[347px]">
            <img
              src="https://images.unsplash.com/photo-1552664730-d307ca884978?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"
              alt="BCDP Team Meeting"
              className="w-full h-48 md:h-full object-cover rounded-lg shadow-lg"
            />
          </div>
          <div className="flex-1 flex flex-col justify-center">
            <Text
              variant="body"
              weight="normal"
              as="p"
              className="text-sm md:text-lg text-gray-600 leading-relaxed text-left lg:text-left"
            >
              The Working Group 1 (WG-1) will be headed by the Secretary, Finance Division, Ministry of Finance, and its members will include the representative, Economic Relations Division, Ministry of Finance; representative, National Board of Revenue, Internal Resources Division, Ministry of Finance; representative, Bangladesh Investment Development Authority, Prime Minister's Office; representative, Ministry concerned with the project; one representative from concerned private sector (nominated by the President); Director General-III,Prime Minister's Office; and Additional Secretary/ Joint Secretary, Ministry of Environment, Forest and Climate Change as the Member Secretary of WG-1. The WG-1's main responsibility is to provide advice on financial resources, both public and private, that are needed to mitigate the impacts of climate change. It will also coordinate all climate financing-related matters, including financing from multilateral and bilateral partners, the private sector, and other sources; and work with the private sector to develop innovative financing instruments, such as green finance.
            </Text>
          </div>
        </div>
      </div>

      {/* Board Composition Table Section - Original design maintained */}
      <div className="max-w-container mx-auto px-3 md:px-4 py-4">
        <Text
          variant="header"
          weight="normal"
          as="h4"
          className="text-primary-500 mb-4 md:mb-6 text-left lg:text-left text-base md:text-xl"
        >
          The Composition Of The BCDP Coordination Board Is As Follows:
        </Text>

        <div className="overflow-x-auto rounded-lg shadow-md border border-gray-100">
          <table className="min-w-full">
            <thead className="bg-gray-100">
              <tr>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
                >
                  #
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
                >
                  Positions
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
                >
                  Details
                </th>
              </tr>
            </thead>
            <tbody className="bg-white">
              {boardComposition.map((item, index) => (
                <tr
                  key={item.id}
                  className={`${
                    index % 2 === 0 ? "bg-light-green-bg" : "bg-white"
                  }`}
                >
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {item.id}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                    {item.position}
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-700">
                    {item.details}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Summary Cards Section - Enhanced mobile responsiveness */}
      <div className="max-w-container mx-auto px-3 md:px-4 py-4 md:py-8">
        <Text
          variant="header"
          weight="normal"
          as="h2"
          className="text-primary-700 mb-4 md:mb-8 text-center lg:text-left text-lg md:text-2xl"
        >
          Summary
        </Text>
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-2 md:gap-6">
          {summaryData.map((card, index) => (
            <SummaryCard key={index} {...card} />
          ))}
        </div>
      </div>

      {/* NAP Sector Table Section - Now using the imported component */}
      <div className="px-3 md:px-0">
        <NapSectorTable onSeeAllClick={handleSeeAllClick} />
      </div>
      
    </div>
  );
};

export default Adaption;