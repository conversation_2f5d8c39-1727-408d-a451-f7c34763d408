import React from "react";
import { Text } from "../../../components"; // Assuming Text component path
import Piechart from "../../../components/Piechart";

// Mock data for the Development Projects Table
const projectData = [
  {
    id: 1,
    projectName: "Project name will appeared here",
    ministry: "Climate Finance (Domestic)",
    status: "In progress",
  },
  {
    id: 2,
    projectName: "Project name will appeared here",
    ministry: "Policy, Knowledge, and Capacity Building",
    status: "Completed",
  },
  {
    id: 3,
    projectName: "Project name will appeared here",
    ministry: "Climate Finance (Domestic)",
    status: "In progress",
  },
  {
    id: 4,
    projectName: "Project name will appeared here",
    ministry: "Policy, Knowledge, and Capacity Building",
    status: "Completed",
  },
  {
    id: 5,
    projectName: "Project name will appeared here",
    ministry: "Policy, Knowledge, and Capacity Building",
    status: "Completed",
  },
  {
    id: 6,
    projectName: "Project name will appeared here",
    ministry: "Policy, Knowledge, and Capacity Building",
    status: "Completed",
  },
];

const DevelopmentSection = ({ pieData, tableData }) => {
  console.log(tableData);
  const projectStatusColors = ["#66FFB0", "#8BC2CA", "#FF6F61", "#FFBC00"];
  return (
    <div className="mt-10 grid grid-cols-12 gap-5">
      {" "}
      {/* Adjusted padding/margin */}
      <div className="col-span-7">
        <Text
          variant="secondary-header"
          weight="normal"
          as="h3"
          className="text-primary-500 mb-3"
        >
          Development Projects By Govt Executing Agencies (Ministries)
        </Text>
        <div className="overflow-x-auto rounded-lg shadow-md border border-gray-100">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-100">
              <tr>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
                >
                  #
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
                >
                  Project name
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
                >
                  Ministry
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
                >
                  Status
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {projectData.map((item, index) => (
                <tr
                  key={item.id}
                  // Use a subtle green for even rows, matching the image
                  className={`${
                    index % 2 === 0 ? "bg-[#F7FFF1]" : "bg-white"
                  }`}
                >
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {item.id}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                    {item.projectName}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                    {item.ministry}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm">
                    <span
                      className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        item.status === "In progress"
                          ? "bg-yellow-100 text-yellow-800" // Yellow badge for 'In progress'
                          : "bg-green-100 text-green-800" // Green badge for 'Completed'
                      }`}
                    >
                      {item.status}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
      <div className="col-span-5">
        <Piechart
          header="Project Status"
          data={pieData}
          colors={projectStatusColors}
        />
      </div>
    </div>
  );
};

export default DevelopmentSection;
