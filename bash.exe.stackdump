Stack trace:
Frame         Function      Args
0007FFFF9890  00021005FEBA (000210285F48, 00021026AB6E, 0007FFFF9890, 0007FFFF8790) msys-2.0.dll+0x1FEBA
0007FFFF9890  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF9B68) msys-2.0.dll+0x67F9
0007FFFF9890  000210046832 (000210285FF9, 0007FFFF9748, 0007FFFF9890, 000000000000) msys-2.0.dll+0x6832
0007FFFF9890  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFF9890  0002100690B4 (0007FFFF98A0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFF9B70  00021006A49D (0007FFFF98A0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF8AF6E0000 ntdll.dll
7FF8AE320000 KERNEL32.DLL
7FF8ACB20000 KERNELBASE.dll
7FF8AF450000 USER32.dll
7FF8ACAF0000 win32u.dll
7FF8AD9A0000 GDI32.dll
7FF8AC9B0000 gdi32full.dll
7FF8AD330000 msvcp_win.dll
7FF8ACFD0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FF8ADC20000 advapi32.dll
7FF8AEBE0000 msvcrt.dll
7FF8ADEA0000 sechost.dll
7FF8AD780000 RPCRT4.dll
7FF8ABF50000 CRYPTBASE.DLL
7FF8AD3E0000 bcryptPrimitives.dll
7FF8ADF50000 IMM32.DLL
