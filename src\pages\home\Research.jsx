import React from 'react';
import { Icon } from '@iconify/react';
import { Text } from '../../components';
import PageHeader from '../../components/PageHeader';
import bcctLogo from '../../assets/bcct.svg';
import bcct2Logo from '../../assets/bcct2.svg';

const data = [
    {
        id: 1,
        leftImage: 'https://picsum.photos/96/96?random=1',
        category: 'প্রতিবেদন',
        titleBn: 'বার্ষিক প্রতিবেদন ২০২৩ - ২০২৪',
        published: 'অক্টোবর-২০২৪',
        titleEn:
            'বাংলাদেশ জলবায়ু পরিবর্তন ট্রাস্ট পরিচালন, বন ও জলবায়ু পরিবর্তন মন্ত্রণালয়',
        publisher: 'বাংলাদেশ জলবায়ু পরিবর্তন ট্রাস্ট (বিসিসিট)',
        ministry:
            'পরিবেশ, বন ও জলবায়ু পরিবর্তন মন্ত্রণালয়',
        ministry2:
            'গণপ্রজাতন্ত্রী বাংলাদেশ সরকার',
        logo: bcctLogo,
    },
    {
        id: 2,
        leftImage: 'https://picsum.photos/96/96?random=2',
        category: 'প্রতিবেদন',
        titleBn:
            'বাংলাদেশ জলবায়ু পরিবর্তন\nট্রাস্ট (বিসিসিটি) ফান্ড-এর\nঅর্থায়নে বিভিন্ন সংস্থা কর্তৃক\nবাস্তবায়িত ৪৫টি প্রকল্পের\nমূল্যায়ন প্রতিবেদন',
        published: 'মার্চ ২০২৪',
        titleEn:
            'বাংলাদেশ জলবায়ু পরিবর্তন ট্রাস্ট (বিসিসিট) ফান্ড-এর অর্থায়নে বিভিন্ন সংস্থা কর্তৃক বাস্তবায়িত ৪৫টি প্রকল্পের মূল্যায়ন প্রতিবেদন',
        publisher: 'বাংলাদেশ জলবায়ু পরিবর্তন ট্রাস্ট (বিসিসিট)',
        ministry:
            'পরিবেশ, বন ও জলবায়ু পরিবর্তন মন্ত্রণালয়',
        ministry2:
            'গণপ্রজাতন্ত্রী বাংলাদেশ সরকার',
        logo: bcctLogo,
    },
    {
        id: 3,
        leftImage: 'https://picsum.photos/96/96?random=3',
        category: 'BUDGET REPORT 2024-25',
        titleBn: 'CLIMATE FINANCING FOR SUSTAINABLE DEVELOPMENT',
        published: 'June 2024',
        titleEn:
            'CLIMATE FINANCING\nFOR SUSTAINABLE\nDEVELOPMENT',
        publisher:
            'Finance Division, Ministry Of Finance ',
        ministry: '',
        ministry2: 'Government Of The  People’s Republic Of Bangladesh',
        logo: bcct2Logo,
    },
];

const Card = ({
    category,
    titleBn,
    published,
    titleEn,
    publisher,
    ministry,
    ministry2,
    logo,
}) => (
    <>
        {/* Desktop Layout - Keep Original */}
        <div className="hidden lg:grid grid-cols-4 gap-6 py-6 border-b border-gray-300">
            {/* Column 1: Category info only */}
            <div className="col-span-1">
                <Text variant="body" weight="bold" className="text-gray-700 text-sm">
                    {category}
                </Text>
                <Text variant="body" className="mt-1 text-sm leading-tight whitespace-pre-line text-gray-500" as="div">
                    {titleBn}
                </Text>
                <Text variant="body" className="mt-2 text-sm text-gray-600" as="div">
                    {published}
                </Text>
            </div>

            {/* Columns 2-3: Main content */}
            <div className="col-span-2">
                <Text
                    variant="body"
                    weight="bold"
                    className="text-gray-600 text-sm leading-tight underline"
                >
                    {titleEn}
                </Text>
                <Text variant="body" className="mt-2 text-sm text-gray-600" as="div">
                    প্রকাশক
                </Text>
                <Text variant="body" className="mt-1 text-sm" as="div">
                    {publisher}
                </Text>
                {ministry && (
                    <Text variant="body" className="mt-1 text-sm text-gray-600" as="div">
                        {ministry}
                    </Text>
                )}
                {ministry2 && (
                    <Text variant="body" className="mt-1 text-sm text-gray-600" as="div">
                        {ministry2}
                    </Text>
                )}
                <button className="mt-3 inline-flex items-center gap-2 bg-green-700 text-white px-4 py-1.5 rounded text-sm">
                    Download <Icon icon="mdi:download" className="text-base" />
                </button>
            </div>

            {/* Column 4: Right logo - full width */}
            <div className="col-span-1 flex items-center justify-center">
                <img
                    src={logo}
                    alt=""
                    className="w-full h-auto object-contain max-h-32"
                />
            </div>
        </div>

        {/* Mobile/Tablet Layout */}
        <div className="lg:hidden border-b border-gray-300 py-4">
            <div className="space-y-4">
                {/* Header Section */}
                <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-3">
                    <div className="flex-1">
                        <Text variant="body" weight="bold" className="text-gray-700 text-sm mb-1">
                            {category}
                        </Text>
                        <Text variant="body" className="text-xs text-gray-600">
                            {published}
                        </Text>
                    </div>
                    <div className="flex-shrink-0 w-full sm:w-32 h-24 sm:h-32">
                        <img
                            src={logo}
                            alt=""
                            className="w-full h-full object-contain"
                        />
                    </div>
                </div>

                {/* Title Section */}
                <div>
                    <Text variant="body" className="text-sm leading-tight whitespace-pre-line text-gray-500 mb-2" as="div">
                        {titleBn}
                    </Text>
                    <Text
                        variant="body"
                        weight="bold"
                        className="text-gray-600 text-sm leading-tight underline whitespace-pre-line"
                    >
                        {titleEn}
                    </Text>
                </div>

                {/* Publisher Section */}
                <div>
                    <Text variant="body" className="text-xs text-gray-600 mb-1" as="div">
                        প্রকাশক
                    </Text>
                    <Text variant="body" className="text-sm mb-1" as="div">
                        {publisher}
                    </Text>
                    {ministry && (
                        <Text variant="body" className="text-sm text-gray-600 mb-1" as="div">
                            {ministry}
                        </Text>
                    )}
                    {ministry2 && (
                        <Text variant="body" className="text-sm text-gray-600" as="div">
                            {ministry2}
                        </Text>
                    )}
                </div>

                {/* Download Button */}
                <div className="pt-2">
                    <button className="inline-flex items-center gap-2 bg-green-700 text-white px-4 py-2 rounded text-sm w-full sm:w-auto justify-center sm:justify-start">
                        Download <Icon icon="mdi:download" className="text-base" />
                    </button>
                </div>
            </div>
        </div>
    </>
);

const Research = ({ className = '' }) => (
    <div className={`bg-white ${className}`}>
        {/* Header with green borders */}
        <div className="border-t-4 border-green-500">
            <div className="border-b border-dashed border-gray-50 p-4">
                <Text variant="header" weight="bold" className="">
                    Latest Research And Publications
                </Text>
            </div>
        </div>

        {/* Content */}
        <div className="p-4 sm:p-6 lg:p-4">
            {data.map((item) => (
                <Card key={item.id} {...item} />
            ))}
        </div>
    </div>
);

export default Research;