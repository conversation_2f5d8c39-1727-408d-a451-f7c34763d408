import React from "react";
import { Text } from ".";
import Chart from "react-google-charts";

const Piechart = ({ header, data = [], colors }) => {
  const chartOptions = {
    is3D: true,
    legend: "none",
    backgroundColor: "transparent",
    chartArea: {
      left: 10,
      top: 10,
      width: "100%",
      height: "100%",
    },
    colors:
      colors ||
      data
        .slice(1)
        .map((_, index) => `hsl(${(index * 360) / data.length}, 70%, 60%)`),
    pieSliceText: "label",
    pieSliceTextStyle: {
      color: "#1F2937",
      fontSize: 14,
      bold: true,
    },
  };

  const legendData = data.slice(1); 
 
  return (
    <div
      className="bg-white p-4 rounded-lg shadow-sm border border-gray-200 flex flex-col h-full"
      style={{
        boxShadow: "0px 0px 12.75px 3.19px rgba(0, 0, 0, 0.08)",
      }}
    >
      <Text
        as="h3"
        variant="body"
        weight="bold"
        className="text-green-600 text-xl mb-4"
      >
        {header}
      </Text>
      <div className="w-full h-64">
        <Chart
          chartType="PieChart"
          data={data}
          options={chartOptions}
          width="100%"
          height="100%"
        />
      </div>
      <div className="flex justify-center mt-4 gap-6 text-sm">
        {legendData.map((item, index) => (
          <div key={index} className="flex items-center gap-2">
            <div
              className="w-4 h-4 rounded-sm"
              style={{
                backgroundColor: chartOptions.colors[index],
              }}
            ></div>
            <span>{item[0]}</span>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Piechart;
