import * as Yup from 'yup';
import {
  Form,
  InputField,
  TextArea,
  Select,
  Checkbox,
  RadioGroup,
  FileUpload
} from './index';
import Text from '../Text';

// Validation schema using Yup
const validationSchema = Yup.object({
  firstName: Yup.string()
    .min(2, 'Must be at least 2 characters')
    .required('First name is required'),
  lastName: Yup.string()
    .min(2, 'Must be at least 2 characters')
    .required('Last name is required'),
  email: Yup.string()
    .email('Invalid email address')
    .required('Email is required'),
  phone: Yup.string()
    .matches(/^[0-9+\-\s()]+$/, 'Invalid phone number'),
  age: Yup.number()
    .min(18, 'Must be at least 18 years old')
    .max(100, 'Must be less than 100 years old'),
  country: Yup.object()
    .nullable()
    .required('Please select a country'),
  skills: Yup.array()
    .min(1, 'Please select at least one skill'),
  bio: Yup.string()
    .max(500, 'B<PERSON> must be less than 500 characters'),
  gender: Yup.string()
    .required('Please select your gender'),
  newsletter: Yup.boolean(),
  terms: Yup.boolean()
    .oneOf([true], 'You must accept the terms and conditions'),
  resume: Yup.mixed()
    .nullable()
    .test('fileSize', 'File too large', (value) => {
      return !value || (value && value.size <= 5 * 1024 * 1024);
    }),
});

// Initial form values
const initialValues = {
  firstName: '',
  lastName: '',
  email: '',
  phone: '',
  age: '',
  country: null,
  skills: [],
  bio: '',
  gender: '',
  newsletter: false,
  terms: false,
  resume: null,
};

// Options for dropdowns
const countryOptions = [
  { value: 'bd', label: 'Bangladesh' },
  { value: 'in', label: 'India' },
  { value: 'pk', label: 'Pakistan' },
  { value: 'us', label: 'United States' },
  { value: 'uk', label: 'United Kingdom' },
];

const skillOptions = [
  { value: 'javascript', label: 'JavaScript' },
  { value: 'react', label: 'React' },
  { value: 'nodejs', label: 'Node.js' },
  { value: 'python', label: 'Python' },
  { value: 'java', label: 'Java' },
];

const genderOptions = [
  { value: 'male', label: 'Male' },
  { value: 'female', label: 'Female' },
  { value: 'other', label: 'Other' },
  { value: 'prefer-not-to-say', label: 'Prefer not to say' },
];

const FormExample = () => {
  const handleSubmit = (values, { setSubmitting, resetForm }) => {
    console.log('Form submitted with values:', values);
    
    // Simulate API call
    setTimeout(() => {
      alert('Form submitted successfully!');
      setSubmitting(false);
      resetForm();
    }, 2000);
  };

  return (
    <div className="max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <div className="mb-6">
        <Text variant="header" weight="bold" className="text-gray-800 mb-2">
          User Registration Form
        </Text>
        <Text variant="body" className="text-gray-600">
          This is an example form showcasing all available form components.
        </Text>
      </div>

      <Form
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
        submitText="Register"
        showReset={true}
        resetText="Clear Form"
      >
        {/* Personal Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <InputField
            name="firstName"
            label="First Name"
            placeholder="Enter your first name"
            required
          />
          
          <InputField
            name="lastName"
            label="Last Name"
            placeholder="Enter your last name"
            required
          />
        </div>

        <InputField
          name="email"
          type="email"
          label="Email Address"
          placeholder="Enter your email"
          required
        />

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <InputField
            name="phone"
            type="tel"
            label="Phone Number"
            placeholder="+880 1234 567890"
          />
          
          <InputField
            name="age"
            type="number"
            label="Age"
            placeholder="Enter your age"
          />
        </div>

        {/* Dropdowns */}
        <Select
          name="country"
          label="Country"
          options={countryOptions}
          placeholder="Select your country"
          required
        />

        <Select
          name="skills"
          label="Skills"
          options={skillOptions}
          placeholder="Select your skills"
          isMulti={true}
          required
        />

        {/* Text Area */}
        <TextArea
          name="bio"
          label="Bio"
          placeholder="Tell us about yourself..."
          rows={4}
        />

        {/* Radio Group */}
        <RadioGroup
          name="gender"
          label="Gender"
          options={genderOptions}
          required
        />

        {/* File Upload */}
        <FileUpload
          name="resume"
          label="Resume"
          accept=".pdf,.doc,.docx"
          maxSize={5 * 1024 * 1024} // 5MB
        />

        {/* Checkboxes */}
        <Checkbox
          name="newsletter"
          label="Subscribe to our newsletter"
        />

        <Checkbox
          name="terms"
          label="I agree to the terms and conditions"
        />
      </Form>
    </div>
  );
};

export default FormExample;
