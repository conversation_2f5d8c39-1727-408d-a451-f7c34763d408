@import url('https://fonts.googleapis.com/css2?family=Archivo:ital,wght@0,100..900;1,100..900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Archivo', sans-serif;
  line-height: 1.6;
  color: #333;
  background-color: #fff;
}

/* Custom Text Classes */
@layer components {
  /* Header Text - 36px */
  .text-header {
    font-family: 'Archivo', sans-serif;
    font-size: 36px;
    font-weight: 600;
    line-height: 1.2;
  }

  /* Secondary Header Text - 20px */
  .text-secondary-header {
    font-family: 'Archivo', sans-serif;
    font-size: 20px;
    font-weight: 500;
    line-height: 1.3;
  }

  /* Body Text and Paragraphs - 16px */
  .text-body {
    font-family: 'Archivo', sans-serif;
    font-size: 16px;
    font-weight: 400;
    line-height: 1.5;
  }

  /* Additional utility classes */
  .text-header-light {
    @apply text-header;
    font-weight: 400;
  }

  .text-header-bold {
    @apply text-header;
    font-weight: 700;
  }

  .text-secondary-header-light {
    @apply text-secondary-header;
    font-weight: 400;
  }

  .text-secondary-header-bold {
    @apply text-secondary-header;
    font-weight: 600;
  }

  .text-body-light {
    @apply text-body;
    font-weight: 300;
  }

  .text-body-medium {
    @apply text-body;
    font-weight: 500;
  }

  .text-body-bold {
    @apply text-body;
    font-weight: 600;
  }
}

#root {
  min-height: 100vh;
}
