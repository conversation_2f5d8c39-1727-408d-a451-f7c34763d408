import { Text } from "../../../components";
import PageHeader from "../../../components/PageHeader";
import Graph2 from "./Graph2"
import Graph1 from "./Graph1"
import Table1 from "./Table1"
import Table2 from "./Table2"
import Table3 from "./Table3"

// Cloud icon component
const CloudIcon = () => (
  <svg 
    width="32" 
    height="32" 
    viewBox="0 0 24 24" 
    fill="none" 
    xmlns="http://www.w3.org/2000/svg"
    className="text-gray-300"
  >
    <path 
      d="M18 10h-1.26A8 8 0 1 0 9 20h9a5 5 0 0 0 0-10z" 
      stroke="currentColor" 
      strokeWidth="1.5" 
      strokeLinecap="round" 
      strokeLinejoin="round"
    />
  </svg>
);



// Updated data for the summary cards to match the new design
const summaryData = [
  {
    number: "12",
    title: "Total climate projects",
    description: "",
  },
  {
    number: "08",
    title: "Projects with full and partial domestic finance",
    description: "",
  },
  {
    number: "06",
    title: "Projects with full and partial external finance",
    description: "",
  },
  {
    number: "06",
    title: "Fund of the climate projects",
    description: "(Total fund, domestic finance, external finance with %)",
  },
];

// Updated SummaryCard component with mobile responsiveness
const SummaryCard = ({ number, title, description }) => (
  <div className="bg-white px-3 sm:px-4 py-3 sm:py-4 rounded-lg border border-[#EDFFEA] hover:shadow-md transition-shadow duration-200 relative overflow-hidden">
    {/* Green background area for cloud icon - responsive sizing */}
    <div className="absolute top-0 right-0 w-12 sm:w-16 h-12 sm:h-16 bg-[#EDFFEA] rounded-bl-2xl sm:rounded-bl-3xl flex items-center justify-center">
      <div className="scale-75 sm:scale-100">
        <CloudIcon />
      </div>
    </div>
    
    <div className="pr-8 sm:pr-12">
      <div className="text-2xl sm:text-3xl font-bold text-green-600 mb-2 sm:mb-3">{number}</div>
      <h3 className="text-gray-800 font-medium text-xs sm:text-sm leading-tight mb-1">{title}</h3>
      {description && (
        <p className="text-xs text-gray-500">{description}</p>
      )}
    </div>
  </div>
);

const Technology = () => {
  // Fixed breadcrumb items with extra spacing
 

  return (
    <div className="min-h-screen bg-white">
     {/* Summary Cards Section with updated design and mobile responsiveness */}
      <div className="max-w-container mx-auto px-4 py-6 md:py-8">
        <Text
          variant="header"
          weight="normal"
          as="h2"
          className="text-primary-700 mb-6 md:mb-8 text-center lg:text-left text-xl sm:text-2xl"
        >
          Summary
        </Text>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6">
          {summaryData.map((card, index) => (
            <SummaryCard key={index} {...card} />
          ))}
        </div>
      </div>

      {/* Graph Section with mobile responsiveness */}
      <section className="py-8 md:py-16 lg:py-10 bg-white">
        <div className="max-w-container mx-auto px-4">
          <Graph2 />
        </div>
      </section>


    
      <section className="py-8 md:py-16 lg:py-10 bg-white">
        <div className="max-w-container mx-auto px-4">
          <Table1/>
        </div>
      </section>


       <section className="py-8 md:py-16 lg:py-10 bg-white">
        <div className="max-w-container mx-auto px-4">
          <Graph1 />
        </div>
      </section>


<h1 className="text-xl font-bold text-center mb-8 text-green-600">
          Nap Investment by Climate Stress Area
        </h1>

            <div className="w-full h-full flex flex-col items-center justify-center max-h-[500px]">
              {/* <img
                src={BangladeshMap}
                alt="Bangladesh Choropleth Map"
                className="w-full h-full object-contain rounded-lg"
              /> */}
              <iframe
                width="600"
                height="400"
                seamless
                frameBorder="0"
                scrolling="no"
                src="http://18.212.44.37:8088/superset/explore/p/OBbKL2kpZ7N/?standalone=1&height=400"
              ></iframe>
            </div>
          

<section className="py-8 md:py-16 lg:py-10 bg-white">
        <div className="max-w-container mx-auto px-4">
          <Table2/>
        </div>
      </section>

          <h1 className="text-xl font-bold text-center mb-8 text-green-600">
          Nap Investment by District
        </h1>

            <div className="w-full h-full flex flex-col items-center justify-center max-h-[500px]">
              {/* <img
                src={BangladeshMap}
                alt="Bangladesh Choropleth Map"
                className="w-full h-full object-contain rounded-lg"
              /> */}
              <iframe
                width="600"
                height="400"
                seamless
                frameBorder="0"
                scrolling="no"
                src="http://18.212.44.37:8088/superset/explore/p/OBbKL2kpZ7N/?standalone=1&height=400"
              ></iframe>
            </div>


            <section className="py-8 md:py-16 lg:py-10 bg-white">
        <div className="max-w-container mx-auto px-4">
          <Table3/>
        </div>
      </section>

          





     
      
    </div>
  );
};

export default Technology;