# TanStack Query & RTK Query Setup Guide

This project is configured with both TanStack Query and RTK Query for comprehensive data fetching and state management.

## Overview

- **TanStack Query**: Used for data fetching with advanced caching, background updates, and query invalidation
- **RTK Query**: Used for CRUD operations with automatic cache management and optimistic updates
- **Redux Toolkit**: For global state management (user authentication)

## Environment Variables

Create a `.env` file in the root directory:

```env
VITE_API_BASE_URL=https://your-api-url.com/api/v1
```

## Authentication Flow

### 1. Login
```javascript
import { useAuth } from './hooks/useAuth';

const { login, user, isAuthenticated } = useAuth();

const handleLogin = async () => {
  const result = await login({
    email: '<EMAIL>',
    password: 'password123'
  });
  
  if (result.success) {
    console.log('Login successful:', result.data);
  } else {
    console.error('Login failed:', result.error);
  }
};
```

### 2. Logout
```javascript
const { logout } = useAuth();

const handleLogout = async () => {
  await logout();
};
```

## TanStack Query Usage

### 1. Data Fetching with Search and Pagination
```javascript
import { useTanstackQuery } from './hooks/useTanstackQuery';

const {
  data,
  isLoading,
  error,
  refetch
} = useTanstackQuery({
  queryKey: 'users',
  endpoint: 'users',
  params: {
    search: searchTerm,
    page: currentPage,
    limit: 10
  },
  enabled: true,
});
```

### 2. Mutations
```javascript
import { useTanstackMutation } from './hooks/useTanstackQuery';

const mutation = useTanstackMutation({
  onSuccess: (data) => {
    console.log('Success:', data);
  },
  onError: (error) => {
    console.error('Error:', error);
  },
  invalidateQueries: ['users'], // Invalidate these queries after mutation
});

// Usage
mutation.mutate({
  endpoint: 'users',
  method: 'POST',
  body: { name: 'John Doe', email: '<EMAIL>' }
});
```

## RTK Query Usage

### 1. Data Fetching
```javascript
import { useGetApiQuery } from './store/api/commonApi';

const {
  data,
  isLoading,
  error,
  refetch
} = useGetApiQuery({
  url: 'users',
  params: {
    search: searchTerm,
    page: currentPage,
    limit: 10
  }
});
```

### 2. CRUD Operations

#### Create (POST)
```javascript
import { usePostApiMutation } from './store/api/commonApi';

const [postApi] = usePostApiMutation();

const handleCreate = async () => {
  try {
    const result = await postApi({
      end_point: 'users',
      body: { name: 'John Doe', email: '<EMAIL>' }
    }).unwrap();
    console.log('Created:', result);
  } catch (error) {
    console.error('Error:', error);
  }
};
```

#### Update (PUT)
```javascript
import { useUpdateApiJsonMutation } from './store/api/commonApi';

const [updateApi] = useUpdateApiJsonMutation();

const handleUpdate = async () => {
  try {
    const result = await updateApi({
      end_point: 'users/1',
      body: { name: 'Jane Doe', email: '<EMAIL>' }
    }).unwrap();
    console.log('Updated:', result);
  } catch (error) {
    console.error('Error:', error);
  }
};
```

#### Update with FormData
```javascript
import { useUpdateApiMutation } from './store/api/commonApi';

const [updateFormData] = useUpdateApiMutation();

const handleUpdateWithFile = async () => {
  const formData = new FormData();
  formData.append('name', 'Jane Doe');
  formData.append('avatar', fileInput.files[0]);
  
  try {
    const result = await updateFormData({
      end_point: 'users/1',
      body: formData
    }).unwrap();
    console.log('Updated:', result);
  } catch (error) {
    console.error('Error:', error);
  }
};
```

#### Delete
```javascript
import { useDeleteApiMutation } from './store/api/commonApi';

const [deleteApi] = useDeleteApiMutation();

const handleDelete = async () => {
  try {
    const result = await deleteApi({
      end_point: 'users/1',
      body: {}
    }).unwrap();
    console.log('Deleted:', result);
  } catch (error) {
    console.error('Error:', error);
  }
};
```

## When to Use What

### Use TanStack Query for:
- Data fetching with complex caching requirements
- Background data synchronization
- Infinite queries (pagination)
- Dependent queries
- Real-time data that needs frequent updates

### Use RTK Query for:
- CRUD operations
- Operations that need optimistic updates
- Complex data transformations
- When you need tight integration with Redux state

## Authentication Headers

Both TanStack Query and RTK Query are configured to automatically include the authentication token in headers when a user is logged in. The token is stored in Redux state and localStorage for persistence.

## Error Handling

Both systems are configured to:
- Automatically logout users on 401 errors
- Retry failed requests (except for auth errors)
- Provide consistent error handling patterns

## Development Tools

- Redux DevTools: Automatically enabled in development
- TanStack Query DevTools: Available in development mode
- Both provide excellent debugging capabilities

## Example Component

See `src/components/ExampleUsage.jsx` for a complete example of how to use both systems together.
