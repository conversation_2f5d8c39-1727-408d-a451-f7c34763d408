// Navigation items for the public navbar
export const publicNavItems = [
  {
    path: '/',
    name: 'Home'
  },
  {
    path: '/about',
    name: 'About BCDP'
  },
  {
    path: '/board',
    name: 'BCDP Board'
  },
  {
    path: '/working-groups',
    name: 'Working Groups',
    hasDropdown: true,
    dropdownItems: [
      { name: 'Climate Finance', path: '/working-groups/climate-finance' },
      { name: 'Adaptation', path: '/working-groups/adaptation' },
      { name: 'Mitigation', path: '/working-groups/mitigation' },
      { name: 'Technology Transfer', path: '/working-groups/technology' },
    ]
  },
  {
    path: '/research',
    name: 'Research And Innovation'
  },
  {
    path: '/publications',
    name: 'Publications'
  },
  {
    path: '/policies',
    name: 'Policies & Laws'
  },
  {
    path: '/contact',
    name: 'Contact Us'
  }
];

// Admin navigation items for the admin sidebar
export const adminNavItems = [
  { name: 'Dashboard', path: '/admin', icon: '📊' },
  { name: 'Users', path: '/admin/users', icon: '👥' },
  { name: 'Content', path: '/admin/content', icon: '📝' },
  { name: 'Settings', path: '/admin/settings', icon: '⚙️' },
];

// Legacy export for backward compatibility
export const navItems = publicNavItems;
