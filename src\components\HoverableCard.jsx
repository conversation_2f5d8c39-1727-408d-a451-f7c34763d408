import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Icon } from '@iconify/react';
import { Text } from './index';

const HoverableCard = ({
  image,
  title,
  author,
  date,
  className = ''
}) => {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <div
      className={`cursor-pointer transition-all duration-500 ease-in-out ${className}`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* White background card with shadow and animated border */}
      <div className={`rounded-lg overflow-hidden transition-all duration-500 ease-in-out relative ${
        isHovered
          ? 'bg-white shadow-lg transform translate-y-[-2px] p-1'
          : 'bg-transparent'
      }`}>

        {/* Static green glow effect around the card edges */}
        {isHovered && (
          <motion.div
            className="absolute -inset-2 rounded-lg pointer-events-none"
            initial={{ opacity: 0 }}
            animate={{
              opacity: [0.6, 1, 0.6],
              boxShadow: [
                '0 0 20px rgba(0, 180, 0, 0.4)',
                '0 0 30px rgba(0, 180, 0, 0.6)',
                '0 0 20px rgba(0, 180, 0, 0.4)'
              ]
            }}
            transition={{
              duration: 2,
              ease: "easeInOut",
              repeat: Infinity
            }}
            style={{
              background: 'rgba(0, 180, 0, 0.15)',
              filter: 'blur(4px)',
              zIndex: -1,
              borderRadius: '8px',
            }}
          />
        )}

        {/* Image section with padding when expanded */}
        <div className={`relative overflow-hidden flex items-center justify-center transition-all duration-500 ease-in-out ${
          isHovered ? 'bg-white p-3 rounded-t-lg' : 'bg-transparent'
        }`}>
          <img
            src={image}
            alt={title}
            className="h-48 w-full object-cover transition-all duration-500 ease-in-out rounded-lg"
          />
        </div>

        {/* Content section - Fixed padding */}
        <div className={`transition-all duration-500 ease-in-out ${
          isHovered ? 'bg-white px-4 pt-4 pb-6 rounded-b-lg' : 'px-0 pt-4 pb-0'
        }`}>
          <Text
            variant="body"
            weight="semibold"
            as="h3"
            className={`text-gray-900 mb-3 leading-tight transition-all duration-500 ease-in-out ${
              isHovered ? 'text-lg line-clamp-none' : 'line-clamp-2'
            }`}
          >
            {title}
          </Text>

          <div className={`flex items-center justify-between text-gray-600 transition-all duration-500 ease-in-out ${
            isHovered ? 'text-base mb-4' : 'text-sm'
          }`}>
            <div className="flex items-center">
              <Icon icon="mdi:account-outline" className={`mr-1 flex-shrink-0 ${
                isHovered ? 'w-5 h-5' : 'w-4 h-4'
              }`} />
              <span>By {author}</span>
            </div>
            <div className="flex items-center">
              <Icon icon="mdi:calendar-outline" className={`mr-1 flex-shrink-0 ${
                isHovered ? 'w-5 h-5' : 'w-4 h-4'
              }`} />
              <span>{date}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HoverableCard;