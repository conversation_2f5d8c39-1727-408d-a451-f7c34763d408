import { Chart } from "react-google-charts";
import Text from "./Text";

const generateRandomColor = () => {
  const letters = "0123456789ABCDEF";
  let color = "#";
  for (let i = 0; i < 6; i++) {
    color += letters[Math.floor(Math.random() * 16)];
  }
  return color;
};

const TrendChart = ({ header, data = [], colors = [] }) => {
  const chartColors =
    colors?.length > 0
      ? colors
      : Array.from({ length: data[0]?.length - 1 }, generateRandomColor);

  const options = {
    curveType: "function",
    backgroundColor: "transparent",
    chartArea: { left: 40, top: 30, width: "85%", height: "75%" },
    legend: { position: "none" },
    colors: chartColors,
    vAxis: {
      format: "#'%'",
      minValue: 0,
      maxValue: 100,
      gridlines: { color: "#E5E7EB" },
    },
  };

  return (
    <div>
      <Text
        as="h3"
        variant="body"
        weight="bold"
        className="text-green-600 text-base mb-4"
      >
        {header}
      </Text>
      <Chart
        chartType="LineChart"
        width="100%"
        height="300px"
        data={data}
        options={options}
      />
    </div>
  );
};

export default TrendChart;
