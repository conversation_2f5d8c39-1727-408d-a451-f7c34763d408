import { Link } from 'react-router-dom';
import { Icon } from '@iconify/react';
import Text from './Text';

const PageHeader = ({ title, breadcrumbs = [] }) => {
  // Render breadcrumb items from props
  const renderBreadcrumbs = () => {
    if (breadcrumbs.length === 0) return null;

    return breadcrumbs.map((breadcrumb, index) => {
      const isLast = index === breadcrumbs.length - 1;

      return (
        <span key={index}>
          {index > 0 && <Text variant="secondary-header" className="mx-3 text-gray-500">/</Text>}
          {isLast || !breadcrumb.path ? (
            <Text variant="secondary-header" className="text-gray-600">{breadcrumb.label}</Text>
          ) : (
            <Link
              to={breadcrumb.path}
              className="text-gray-600 hover:text-primary-600 transition-colors"
            >
              <Text variant="secondary-header">{breadcrumb.label}</Text>
            </Link>
          )}
        </span>
      );
    });
  };

  return (
    <div className="bg-breadcrumb-bg h-[160px] sm:h-[170px] md:h-[180px] shadow-md rounded-b-lg">
      <div className="max-w-container mx-auto px-4 sm:px-6 lg:px-8 h-full relative">
        {/* Breadcrumb navigation - Top Left within container */}
        <div className="absolute top-3 left-4 sm:top-4 sm:left-6 lg:left-8 flex items-center text-xs sm:text-sm">
          <Link to="/" className="text-gray-600 hover:text-primary-600 transition-colors">
            <Icon
              icon="mdi:home-outline"
              className="w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 inline mr-2"
            />
          </Link>
          {renderBreadcrumbs()}
        </div>

        {/* Title section - Center */}
        <div className="h-full flex items-center justify-center px-4 sm:px-8">
          <Text
            variant="header"
            className="font-bold text-center text-2xl sm:text-3xl lg:text-3xl mt-4"
            style={{ color: '#72716F' }}
          >
            {title}
          </Text>
        </div>
      </div>
    </div>
  );
};

export default PageHeader;