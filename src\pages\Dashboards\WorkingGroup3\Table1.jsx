import { Text } from "../../../components";
import PageHeader from "../../../components/PageHeader";

// Data for the board composition table (unchanged)


// Updated summary data with realistic figures
const summaryData = [
  {
    number: "12",
    title: "Total climate projects",
    description: "Covering adaptation and mitigation initiatives",
  },
  {
    number: "08",
    title: "Projects with full and partial domestic finance",
    description: "67% of total projects funded domestically",
  },
  {
    number: "06",
    title: "Projects with full and partial external finance",
    description: "50% of projects with international funding",
  },
  {
    number: "USD 2.8B",
    title: "Total fund of the climate projects",
    description: "(Domestic: 45%, External: 55%)",
  },
];

// NAP Sector Projects Data
const napSectorProjects = [
  {
    id: 1,
    sector: "Water resources",
    estimatedFund: "USD 450 million",
    totalInvestment: "USD 520 million",
    financingGap: "USD 70 million"
  },
  {
    id: 2,
    sector: "Disaster, social safety and security",
    estimatedFund: "USD 380 million",
    totalInvestment: "USD 425 million",
    financingGap: "USD 45 million"
  },
  {
    id: 3,
    sector: "Agriculture",
    estimatedFund: "USD 320 million",
    totalInvestment: "USD 390 million",
    financingGap: "USD 70 million"
  },
  {
    id: 4,
    sector: "Fisheries, aquaculture and livestock",
    estimatedFund: "USD 280 million",
    totalInvestment: "USD 340 million",
    financingGap: "USD 60 million"
  },
  {
    id: 5,
    sector: "Urban areas",
    estimatedFund: "USD 250 million",
    totalInvestment: "USD 310 million",
    financingGap: "USD 60 million"
  },
  {
    id: 6,
    sector: "Ecosystems, wetlands and biodiversity",
    estimatedFund: "USD 200 million",
    totalInvestment: "USD 265 million",
    financingGap: "USD 65 million"
  },
  {
    id: 7,
    sector: "Policies and institutions",
    estimatedFund: "USD 180 million",
    totalInvestment: "USD 220 million",
    financingGap: "USD 40 million"
  },
  {
    id: 8,
    sector: "Capacity development, research and innovation",
    estimatedFund: "USD 150 million",
    totalInvestment: "USD 195 million",
    financingGap: "USD 45 million"
  }
];

// NDC Sector Projects Data
const ndcSectorProjects = [
  {
    id: 1,
    sector: "Energy (Power, Industry, Transport)",
    estimatedFund: "USD 850 million",
    totalInvestment: "USD 980 million",
    financingGap: "USD 130 million"
  },
  {
    id: 2,
    sector: "Industrial Process and Product Use (IPPU)",
    estimatedFund: "USD 420 million",
    totalInvestment: "USD 495 million",
    financingGap: "USD 75 million"
  },
  {
    id: 3,
    sector: "Agriculture, Forestry and Other Land Use (AFOLU)",
    estimatedFund: "USD 380 million",
    totalInvestment: "USD 450 million",
    financingGap: "USD 70 million"
  },
  {
    id: 4,
    sector: "Waste",
    estimatedFund: "USD 220 million",
    totalInvestment: "USD 275 million",
    financingGap: "USD 55 million"
  }
];

const Technology = () => {
  return (
    <div className="min-h-screen bg-white">
     


      {/* NAP Sector Projects Table */}
      <div className="max-w-container mx-auto px-4">
         <span className=" mb-6 inline-block bg-green-700 text-white px-4 py-3 text-2xl rounded-tr-2xl rounded-bl-lg font-semibold shadow-md">
          Table
        </span>
        <div className="mb-4 flex justify-between items-center">
          <Text
            variant="header"
            weight="normal"
            as="h4"
            className="text-primary-500 text-lg sm:text-xl md:text-xl"
          >
            Sector Wise Projects And Investment
          </Text>
          <button className="text-green-600 hover:text-green-700 text-sm font-medium">
            See All
          </button>
        </div>

        <div className="overflow-x-auto rounded-lg shadow-md border border-gray-100 mb-8">
          <table className="min-w-full">
            <thead className="bg-gray-100">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                  # NAP Sector
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                  Estimated fund requirement as per NAP
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                  Total Investment (completed and ongoing)
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                  Financing gap
                </th>
              </tr>
            </thead>
            <tbody className="bg-white">
              {napSectorProjects.map((item, index) => (
                <tr
                  key={item.id}
                  className={`${
                    index % 2 === 0 ? "bg-light-green-bg" : "bg-white"
                  }`}
                >
                  <td className="px-6 py-4 text-sm text-gray-700">
                    {item.id}. {item.sector}
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-700">
                    {item.estimatedFund}
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-700">
                    {item.totalInvestment}
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-700">
                    {item.financingGap}
                  </td>
                </tr>
              ))}
              <tr className="bg-green-100 font-semibold">
                <td className="px-6 py-4 text-sm text-gray-900">
                  Total
                </td>
                <td className="px-6 py-4 text-sm text-gray-900">
                  USD 2.21 billion
                </td>
                <td className="px-6 py-4 text-sm text-gray-900">
                  USD 2.67 billion
                </td>
                <td className="px-6 py-4 text-sm text-gray-900">
                  USD 460 million
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        {/* NDC Sector Projects Table */}
        <div className="mb-4 flex justify-between items-center">
          <Text
            variant="header"
            weight="normal"
            as="h4"
            className="text-primary-500 text-lg sm:text-xl md:text-xl"
          >
            Sector Wise Projects And Investment
          </Text>
          <button className="text-green-600 hover:text-green-700 text-sm font-medium">
            See All
          </button>
        </div>

        <div className="overflow-x-auto rounded-lg shadow-md border border-gray-100">
          <table className="min-w-full">
            <thead className="bg-gray-100">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                  # NDC Sector
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                  Estimated fund requirement as per NAP
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                  Total Investment (completed and ongoing)
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                  Financing gap
                </th>
              </tr>
            </thead>
            <tbody className="bg-white">
              {ndcSectorProjects.map((item, index) => (
                <tr
                  key={item.id}
                  className={`${
                    index % 2 === 0 ? "bg-light-green-bg" : "bg-white"
                  }`}
                >
                  <td className="px-6 py-4 text-sm text-gray-700">
                    {item.id}. {item.sector}
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-700">
                    {item.estimatedFund}
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-700">
                    {item.totalInvestment}
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-700">
                    {item.financingGap}
                  </td>
                </tr>
              ))}
              <tr className="bg-green-100 font-semibold">
                <td className="px-6 py-4 text-sm text-gray-900">
                  Total
                </td>
                <td className="px-6 py-4 text-sm text-gray-900">
                  USD 1.87 billion
                </td>
                <td className="px-6 py-4 text-sm text-gray-900">
                  USD 2.20 billion
                </td>
                <td className="px-6 py-4 text-sm text-gray-900">
                  USD 330 million
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default Technology;