import React from 'react';
import { Text } from './index';

const TextDemo = () => {
  return (
    <div className="p-8 max-w-4xl mx-auto space-y-8">
      <div className="border-b pb-4">
        <h1 className="text-2xl font-bold mb-4">Archivo Font Demo</h1>
        <p className="text-gray-600">All text styles using Archivo font family</p>
      </div>

      {/* Header Text - 36px */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold text-gray-800">Header Text (36px)</h2>
        <div className="space-y-2">
          <Text variant="header" weight="light" as="h1">
            Header Light - Bangladesh Climate Development Partnership
          </Text>
          <Text variant="header" weight="normal" as="h1">
            Header Normal - Bangladesh Climate Development Partnership
          </Text>
          <Text variant="header" weight="bold" as="h1">
            Header Bold - Bangladesh Climate Development Partnership
          </Text>
        </div>
        
        {/* Using CSS classes directly */}
        <div className="space-y-2 mt-4">
          <p className="text-sm text-gray-600">Using CSS classes directly:</p>
          <h1 className="text-header">Direct CSS Class - Main Heading</h1>
          <h1 className="text-header-light">Direct CSS Class - Light Heading</h1>
          <h1 className="text-header-bold">Direct CSS Class - Bold Heading</h1>
        </div>
      </div>

      {/* Secondary Header Text - 20px */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold text-gray-800">Secondary Header Text (20px)</h2>
        <div className="space-y-2">
          <Text variant="secondary-header" weight="light" as="h2">
            Secondary Header Light - Research & Publications
          </Text>
          <Text variant="secondary-header" weight="normal" as="h2">
            Secondary Header Normal - Research & Publications
          </Text>
          <Text variant="secondary-header" weight="bold" as="h2">
            Secondary Header Bold - Research & Publications
          </Text>
        </div>

        {/* Using CSS classes directly */}
        <div className="space-y-2 mt-4">
          <p className="text-sm text-gray-600">Using CSS classes directly:</p>
          <h2 className="text-secondary-header">Direct CSS Class - Section Title</h2>
          <h2 className="text-secondary-header-light">Direct CSS Class - Light Section</h2>
          <h2 className="text-secondary-header-bold">Direct CSS Class - Bold Section</h2>
        </div>
      </div>

      {/* Body Text - 16px */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold text-gray-800">Body Text (16px)</h2>
        <div className="space-y-4">
          <Text variant="body" weight="light" as="p">
            Body Light - Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris.
          </Text>
          <Text variant="body" weight="normal" as="p">
            Body Normal - Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris.
          </Text>
          <Text variant="body" weight="medium" as="p">
            Body Medium - Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris.
          </Text>
          <Text variant="body" weight="bold" as="p">
            Body Bold - Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris.
          </Text>
        </div>

        {/* Using CSS classes directly */}
        <div className="space-y-2 mt-4">
          <p className="text-sm text-gray-600">Using CSS classes directly:</p>
          <p className="text-body">Direct CSS Class - Regular paragraph text with normal weight.</p>
          <p className="text-body-light">Direct CSS Class - Light paragraph text for subtle content.</p>
          <p className="text-body-medium">Direct CSS Class - Medium paragraph text for emphasis.</p>
          <p className="text-body-bold">Direct CSS Class - Bold paragraph text for strong emphasis.</p>
        </div>
      </div>

      {/* Tailwind Font Classes */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold text-gray-800">Tailwind Font Classes</h2>
        <div className="space-y-2">
          <p className="text-sm text-gray-600">You can also use Tailwind classes:</p>
          <p className="font-archivo text-header">Using font-archivo with text-header</p>
          <p className="font-sans text-secondary-header">Using font-sans with text-secondary-header</p>
          <p className="font-archivo text-body">Using font-archivo with text-body</p>
        </div>
      </div>

      {/* Usage Examples */}
      <div className="space-y-4 bg-gray-50 p-6 rounded-lg">
        <h2 className="text-xl font-semibold text-gray-800">Usage Examples</h2>
        <div className="space-y-4 text-sm">
          <div>
            <p className="font-semibold mb-2">Component Usage:</p>
            <code className="bg-gray-200 p-2 rounded block">
              {`<Text variant="header" weight="bold" as="h1">Main Title</Text>`}
            </code>
          </div>
          <div>
            <p className="font-semibold mb-2">CSS Class Usage:</p>
            <code className="bg-gray-200 p-2 rounded block">
              {`<h1 className="text-header-bold">Main Title</h1>`}
            </code>
          </div>
          <div>
            <p className="font-semibold mb-2">Tailwind Usage:</p>
            <code className="bg-gray-200 p-2 rounded block">
              {`<h1 className="font-archivo text-header font-bold">Main Title</h1>`}
            </code>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TextDemo;
