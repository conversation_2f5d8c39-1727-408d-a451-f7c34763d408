import React from 'react';
import * as Yup from 'yup';
import { Form, InputField, TextArea } from '../../components/forms';
import { Text } from '../../components';

// Validation schema
const feedbackValidationSchema = Yup.object({
  name: Yup.string()
    .min(2, 'Name must be at least 2 characters')
    .max(100, 'Name must be less than 100 characters')
    .required('Name is required'),

  email: Yup.string()
    .email('Please enter a valid email address')
    .required('Email is required'),

  subject: Yup.string()
    .min(5, 'Subject must be at least 5 characters')
    .max(200, 'Subject must be less than 200 characters')
    .required('Subject is required'),

  message: Yup.string()
    .min(10, 'Message must be at least 10 characters')
    .max(1000, 'Message must be less than 1000 characters')
    .required('Message is required'),
});

// Initial form values
const initialValues = {
  name: '',
  email: '',
  subject: '',
  message: '',
};

const FeedbackForm = ({ className = '' }) => {
  const handleSubmit = async (values, { setSubmitting, resetForm }) => {
    setSubmitting(false);
    resetForm();
  };

  return (
    <div className={`w-full ${className}`}>
      {/* Header */}
      <div className="mb-8">
        <Text variant="header" weight="bold" as="h2" className="text-gray-800 mb-2">
          Share Your Feedback
        </Text>
        <div className="w-full h-1 bg-gradient-to-r from-green-500 to-green-300 mb-4"></div>
      </div>

      {/* Form */}
      <div className="w-full bg-white border-2 border-primary-400 rounded-lg p-6">
        <Form
          initialValues={initialValues}
          validationSchema={feedbackValidationSchema}
          onSubmit={handleSubmit}
          submitText="Submit"
          showReset={false}
          submitButtonProps={{
            className: "bg-green-600 hover:bg-green-700 text-white px-8 py-3 rounded-md font-medium transition-colors duration-200 ml-auto"
          }}
          actionsClassName="flex justify-end"
        >
          {/* Name and Email Row */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <InputField
              name="name"
              type="text"
              label="Name"
              placeholder="Enter your name"
              required
            />

            <InputField
              name="email"
              type="email"
              label="Email Address"
              placeholder="Enter email address"
              required
            />
          </div>

          {/* Subject */}
          <div className="mb-4">
            <InputField
              name="subject"
              type="text"
              label="Subject"
              placeholder="Enter your name"
              required
            />
          </div>

          {/* Feedback Message */}
          <div className="mb-4">
            <TextArea
              name="message"
              label="Feedback"
              placeholder="Write your valuable feedback here"
              rows={4}
              required
            />
          </div>
        </Form>
      </div>
    </div>
  );
};

export default FeedbackForm;
