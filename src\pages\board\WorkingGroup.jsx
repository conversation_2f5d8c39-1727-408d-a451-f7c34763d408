import sectionbg1 from "../../assets/sectionbg1.jpg";
import sectionbg2 from "../../assets/sectionbg2.jpg";
import { Text } from "../../components";

const WorkingGroup = ({ isEven = false }) => {
  // Accept the isEven prop

  // Define the JSX for the left-side staggered design block
  const designBlock = (
    <div className="flex w-full h-[400px] sm:h-[500px] lg:w-[625px] lg:h-[645px] overflow-hidden order-1 lg:order-none">
      {/* Left Column - Shifted down by 16px */}
      <div className="flex flex-col w-1/2 mt-3 sm:mt-4 lg:mt-6">
        {/* Top section - Text with purple background */}
        <div className="bg-[#804080] p-3 sm:p-5 lg:p-8 text-white flex-1 flex flex-col justify-center">
          <Text
            variant="header"
            weight="normal"
            as="h2"
            className="mb-2 sm:mb-3 lg:mb-4 text-white text-sm sm:text-base lg:text-xl"
          >
            Demonstrating
            <br />
            New Approach
          </Text>
          <Text
            variant="body"
            weight="normal"
            as="p"
            className="text-white opacity-80 text-xs sm:text-sm lg:text-base leading-relaxed"
          >
            Climate projects will form the common basis for engaging with
            development partners will support for climate actions
          </Text>
        </div>
        {/* Bottom section - Image with light gray background */}
        <div className="flex-1 flex items-center justify-center overflow-hidden">
          <img
            src={sectionbg2} // Uses imported image variable
            alt="New Approach"
            className="w-full h-full object-cover"
          />
        </div>
      </div>

      {/* Right Column - Shifted up by 16px */}
      <div className="flex flex-col w-1/2 mb-3 sm:mb-4 lg:mb-6">
        {/* Top section - Image with green background */}
        <div className="bg-primary-600 flex-1 flex items-center justify-center overflow-hidden">
          <img
            src={sectionbg1} // Uses imported image variable
            alt="Development Vision"
            className="w-full h-full object-cover"
          />
        </div>
        {/* Bottom section - Text with green background */}
        <div className="bg-primary-700 p-3 sm:p-5 lg:p-8 text-white flex-1 flex flex-col justify-center">
          <Text
            variant="header"
            weight="normal"
            as="h2"
            className="mb-2 sm:mb-3 lg:mb-4 text-white text-sm sm:text-base lg:text-xl"
          >
            Development
            <br />
            Vision
          </Text>
          <Text
            variant="body"
            weight="normal"
            as="p"
            className="text-white opacity-80 text-xs sm:text-sm lg:text-base leading-relaxed"
          >
            Bangladesh has set out climate ambitions as part of its development
            vision for Perspective Plan of Bangladesh 2021-2041
          </Text>
        </div>
      </div>
    </div>
  );

  // Define the JSX for the right-side Working Group details block
  const detailsBlock = (
    <div className="lg:ml-10 px-4 lg:px-0 order-2 lg:order-none">
      {" "}
      <div className="mb-1">
        {" "}
        <span className="inline-block mb-2 ml-4 bg-[#D3FFC5] text-green-800 text-xs sm:text-sm px-3 sm:px-4 lg:px-5 py-1 sm:py-1.5 shadow-lg rounded-full font-medium">
          Working Group 1
        </span>
      </div>
      <div className="flex items-start mb-4">
        {" "}
        <div className="border-l-2 sm:border-l-4 border-primary-500 h-12 sm:h-16 lg:h-20 mr-2 sm:mr-3 lg:mr-4 flex-shrink-0"></div>
        <Text
          variant="header"
          weight="bold"
          as="h1"
          className="mb-0 mt-0 text-[34px]"
        >
          Working Group 1: Climate Finance (Domestic)
        </Text>
      </div>
      <p className="text-gray-600 leading-relaxed mb-4 sm:mb-6 text-sm sm:text-base">
        The Working Group 1 (WG-1) will be headed by the Secretary, Finance
        Division, Ministry of Finance, and its members will include the
        representative, Economic Relations Division, Ministry of Finance;
        representative, National Board of Revenue
        <span className="text-primary-500 hover:underline cursor-pointer">
          {" "}
          See more
        </span>
      </p>
    </div>
  );

  return (
    <div className="py-4 sm:py-6 lg:py-8">
      <div className="flex flex-col lg:grid lg:grid-cols-2 gap-4 sm:gap-6 lg:gap-8 lg:items-center">
        {isEven ? (
          <>
            {detailsBlock}
            {designBlock}
          </>
        ) : (
          <>
            {designBlock}
            {detailsBlock}
          </>
        )}
      </div>
    </div>
  );
};

export default WorkingGroup;
