import { Routes, Route } from "react-router-dom";
import { PublicLayout, AuthLayout, AdminLayout } from "../layout";
import ProtectedRoute from "../components/ProtectedRoute";

// Import page components
import Home from "../pages/home";
import About from "../pages/about";
import Board from "../pages/board";
import WorkingGroups from "../pages/working-groups";
import Adaption from "../pages/working-groups/adaption";
import Mitigation from "../pages/working-groups/mitigation";
import Technology from "../pages/working-groups/technology";
import Research from "../pages/research";
import Publications from "../pages/publications";
import Policies from "../pages/policies";
import Contact from "../pages/contact";
import Login from "../pages/auth/Login";
import NotFound from "../pages/NotFound";
import { Dashboard, Users, Content, Settings } from "../pages/admin";
import UserDashboard from "../pages/Dashboards/UserDashboard/UserDashboard";
import ProjectList from "../pages/Dashboards/UserDashboard/ProjectList";
import WorkingGruopFourDashboard from '../pages/Dashboards/WorkingGroup4/WokringGroupFourDashboard';
import WorkingGruopThreeDashboard from '../pages/Dashboards/WorkingGroup3/WokringGroupThreeDashboard';
import ProjectInformation from "../pages/ProjectInformation/projectInformation";
import WorkingGroupOneDashboard from "../pages/Dashboards/WorkingGroup1/WorkingGroupOneDashboard";

const AppRoutes = () => {
  return (
    <Routes>
      {/* Auth routes (with AuthLayout) */}
      <Route element={<AuthLayout />}>
        <Route path="/login" element={<Login />} />
      </Route>

      {/* Public routes (with PublicLayout) */}
      <Route element={<PublicLayout />}>
        <Route path="/" element={<Home />} />
        <Route path="/about" element={<About />} />
        <Route path="/board" element={<Board />} />
        <Route path="/working-groups" element={<WorkingGroups />} />
        <Route
          path="/working-groups/climate-finance"
          element={<WorkingGroups />}
        />
        <Route path="/working-groups/adaptation" element={<Adaption />} />
        <Route path="/working-groups/mitigation" element={<Mitigation />} />
        <Route path="/working-groups/technology" element={<Technology />} />
        <Route path="/research" element={<Research />} />
        <Route path="/publications" element={<Publications />} />
        <Route path="/policies" element={<Policies />} />
        <Route path="/dashboard" element={<UserDashboard />} />
        <Route path="/project-list" element={<ProjectList />} />
        <Route path="/working-group-three-dashboard" element={<WorkingGruopThreeDashboard/>}/>
        <Route
          path="/working-group-four-dashboard"
          element={<WorkingGruopFourDashboard />}
        />
        <Route
          path="/working-group-one-dashboard"
          element={<WorkingGroupOneDashboard />}
        />
        <Route path="/contact" element={<Contact />} />
        <Route path="/project-information" element={<ProjectInformation />} />
      </Route>

      {/* Admin routes (with AdminLayout and protection) */}
      {/* <Route path="/admin" element={
        <ProtectedRoute requireAdmin={true}>
          <AdminLayout />
        </ProtectedRoute>
      }>
        <Route index element={<Dashboard />} />
        <Route path="users" element={<Users />} />
        <Route path="content" element={<Content />} />
        <Route path="settings" element={<Settings />} />
      </Route> */}

      {/* 404 Route */}
      <Route path="*" element={<NotFound />} />
    </Routes>
  );
};

export default AppRoutes;
