import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../../hooks/useAuth';
import { Form, InputField, Checkbox } from '../../components/forms';
import { Icon } from '@iconify/react';
import loginbg from '../../assets/loginbg.png';
import * as Yup from 'yup';

const validationSchema = Yup.object({
  email: Yup.string().email('Invalid email address').required('Email is required'),
  password: Yup.string().min(6, 'Password must be at least 6 characters').required('Password is required'),
});

const initialValues = {
  email: '<EMAIL>',
  password: 'bcdp1234',
  rememberMe: false
};

const Login = () => {
  const navigate = useNavigate();
  const { login } = useAuth();

  const handleSubmit = async (values, { setSubmitting, setStatus }) => {
    setStatus(null);

    const result = await login({
      email: values.email,
      password: values.password,
    });

    if (result.success) {
      navigate('/dashboard');
    } else {
      setStatus(result.error);
    }

    setSubmitting(false);
  };

  return (
    <div className="min-h-screen relative">
      <div
        className="absolute top-0 left-0 w-full h-[45vh] bg-cover bg-center bg-no-repeat"
        style={{ backgroundImage: `url(${loginbg})` }}
      />

      <div className="relative z-10 pt-16 pb-8 text-center">
        <h1 className="text-4xl font-bold text-primary-600 mb-4">Welcome !</h1>
        <p className="text-gray-600 text-lg px-4">
          A Partnership to Drive Climate Actions at<br />
          Scale and with Urgency
        </p>
      </div>

      <div className="relative z-10 flex items-center justify-center px-4">
        <div className="w-full max-w-md">
          <div className="bg-white rounded-md shadow-xl border border-gray-100 p-8">
            <div className="text-center mb-8">
              <h2 className="text-2xl font-bold text-gray-800 mb-2">
                Login With
              </h2>
            </div>

            <div className="flex gap-4 mb-6 justify-center">
              <button className="w-[75px] h-[75px] flex items-center justify-center bg-green-50 border border-green-100 rounded-md hover:bg-green-100 transition-colors duration-200">
                <Icon icon="logos:google-icon" className="w-6 h-6" />
              </button>
              <button className="w-[75px] h-[75px] flex items-center justify-center bg-green-50 border border-green-100 rounded-md hover:bg-green-100 transition-colors duration-200">
                <Icon icon="material-symbols:phone-android" className="w-6 h-6 text-green-600" />
              </button>
            </div>

            <div className="relative mb-6">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-200" />
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-4 bg-white text-gray-500">or</span>
              </div>
            </div>

            <Form
              initialValues={initialValues}
              validationSchema={validationSchema}
              onSubmit={handleSubmit}
              submitText="Login"
              submitButtonProps={{
                className: "w-full bg-green-600 hover:bg-green-700 text-white py-3 rounded-xl font-medium transition-colors duration-200"
              }}
              formClassName="space-y-4"
            >
              {({ isSubmitting, status }) => (
                <>
                  {status && (
                    <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                      <p className="text-sm text-red-700">{status}</p>
                    </div>
                  )}

                  <InputField
                    name="email"
                    type="email"
                    label="User Name"
                    placeholder="Write User Name"
                    rightIcon="mdi:pencil-outline"
                    required
                  />

                  <InputField
                    name="password"
                    type="password"
                    label="Password"
                    placeholder="Write Password"
                    showPasswordToggle={true}
                    required
                  />

                  <div className="flex items-center justify-between">
                    <Checkbox
                      name="rememberMe"
                      label="Remember Me"
                      className="text-sm text-gray-600"
                    />
                    <Link
                      to="/forgot-password"
                      className="text-sm text-blue-600 hover:text-blue-700"
                    >
                      Forgot password?
                    </Link>
                  </div>
                </>
              )}
            </Form>

            <div className="mt-6 text-center">
              <Link to="/" className="text-sm text-gray-600 hover:text-gray-800">
                Back to Homepage
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;
