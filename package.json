{"name": "adb_frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@iconify/react": "^6.0.0", "@reduxjs/toolkit": "^2.5.0", "@tailwindcss/vite": "^4.1.8", "@tanstack/react-query": "^5.62.7", "@tanstack/react-query-devtools": "^5.62.7", "formik": "^2.4.6", "framer-motion": "^12.18.1", "react": "^19.1.0", "react-datepicker": "^8.4.0", "react-dom": "^19.1.0", "react-google-charts": "^5.2.1", "react-redux": "^9.2.0", "react-router-dom": "^7.6.2", "react-select": "^5.10.1", "recharts": "^2.15.3", "yup": "^1.6.1"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "tailwindcss": "^3.4.17", "vite": "^6.3.5"}}