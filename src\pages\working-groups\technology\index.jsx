import { Text } from "../../../components";
import PageHeader from "../../../components/PageHeader";
import Graph from "../Graph2"

// Cloud icon component
const CloudIcon = () => (
  <svg 
    width="32" 
    height="32" 
    viewBox="0 0 24 24" 
    fill="none" 
    xmlns="http://www.w3.org/2000/svg"
    className="text-gray-300"
  >
    <path 
      d="M18 10h-1.26A8 8 0 1 0 9 20h9a5 5 0 0 0 0-10z" 
      stroke="currentColor" 
      strokeWidth="1.5" 
      strokeLinecap="round" 
      strokeLinejoin="round"
    />
  </svg>
);

// Data for the board composition table (unchanged)
const boardComposition = [
  {
    id: 1,
    position: "Chair",
    details:
      "Honorable Principal Secretary to the Chief Advisor's Office/Prime Minister's Office",
  },
  {
    id: 2,
    position: "Member",
    details: "Secretary, Planning Division, Ministry of Planning",
  },
  {
    id: 3,
    position: "Member",
    details: "Secretary, Finance Division, Ministry of Finance",
  },
  {
    id: 4,
    position: "Member",
    details: "Secretary, Economic Relations Division, Ministry of Finance",
  },
  {
    id: 5,
    position: "Member",
    details:
      "Secretary, Implementation, Monitoring and Evaluation Division, Ministry of Planning",
  },
  {
    id: 6,
    position: "Member",
    details:
      "Executive Chairman, Bangladesh Investment Development Authority (BIDA), Prime Minister's Office",
  },
  {
    id: 7,
    position: "Member",
    details: "Secretary, Ministry of Disaster Management and Relief",
  },
  {
    id: 8,
    position: "Member",
    details:
      "Secretary, Local Government Division, Ministry of Local Government, Rural Development and Co-operatives",
  },
  {
    id: 9,
    position: "Member",
    details: "Secretary, Ministry of Water Resources",
  },
  { id: 10, position: "Member", details: "Secretary, Ministry of Agriculture" },
  {
    id: 11,
    position: "Member",
    details:
      "One (1) Representative of Private Sector (nominated by the Chairperson)",
  },
  {
    id: 12,
    position: "Member",
    details: "Two (2) Representatives from Development Partners",
  },
  {
    id: 13,
    position: "Member",
    details: "Secretary, Ministry of Environment, Forest and Climate Change",
  },
];

// Updated data for the summary cards to match the new design
const summaryData = [
  {
    number: "12",
    title: "Total climate projects",
    description: "",
  },
  {
    number: "08",
    title: "Projects with full and partial domestic finance",
    description: "",
  },
  {
    number: "06",
    title: "Projects with full and partial external finance",
    description: "",
  },
  {
    number: "06",
    title: "Fund of the climate projects",
    description: "(Total fund, domestic finance, external finance with %)",
  },
];

// Updated SummaryCard component with mobile responsiveness
const SummaryCard = ({ number, title, description }) => (
  <div className="bg-white px-3 sm:px-4 py-3 sm:py-4 rounded-lg border border-[#EDFFEA] hover:shadow-md transition-shadow duration-200 relative overflow-hidden">
    {/* Green background area for cloud icon - responsive sizing */}
    <div className="absolute top-0 right-0 w-12 sm:w-16 h-12 sm:h-16 bg-[#EDFFEA] rounded-bl-2xl sm:rounded-bl-3xl flex items-center justify-center">
      <div className="scale-75 sm:scale-100">
        <CloudIcon />
      </div>
    </div>
    
    <div className="pr-8 sm:pr-12">
      <div className="text-2xl sm:text-3xl font-bold text-green-600 mb-2 sm:mb-3">{number}</div>
      <h3 className="text-gray-800 font-medium text-xs sm:text-sm leading-tight mb-1">{title}</h3>
      {description && (
        <p className="text-xs text-gray-500">{description}</p>
      )}
    </div>
  </div>
);

const Technology = () => {
  // Fixed breadcrumb items with extra spacing
  const breadcrumbs = [
    { label: "Home", path: "/" },
    { label: "Working Group 4: Project Development Support, Monitoring, and Evaluation" } // Current page, no path needed
  ];

  return (
    <div className="min-h-screen bg-white">
      {/* Mobile Header - Only shows on mobile */}
      <div className="block md:hidden px-4 py-4 bg-breadcrumb-bg">
        {/* Mobile Breadcrumbs */}
        <nav className="mb-3">
          <div className="flex items-center space-x-1 text-xs text-gray-600">
            <a href="/" className="hover:text-gray-800 transition-colors">Home</a>
            <span>/</span>
            <span className="text-gray-800 font-medium">Working Group 4</span>
          </div>
        </nav>
        
        {/* Mobile Title */}
        <div className="mb-4">
          <h1 className="text-lg sm:text-xl font-bold text-gray-900 leading-tight">
            Working Group 4: Project Development Support, Monitoring, and Evaluation
          </h1>
        </div>
        
        {/* Mobile Contact Button */}
        {/* <button 
          onClick={() => console.log("Contact board clicked")}
          className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg font-medium text-sm transition-colors duration-200 w-full sm:w-auto"
        >
          Contact Board
        </button> */}
      </div>

      {/* Desktop Header - Only shows on desktop */}
      <div className="hidden md:block [&_nav_a]:mx-3 [&_nav_span]:mx-3 [&_nav>*:first-child]:ml-0">
        <PageHeader 
          title={
            <>
              Working Group 4: <br />
              Project Development Support, Monitoring, and Evaluation
            </>
          }
          breadcrumbs={breadcrumbs}
          buttonText="Contact Board"
          buttonOnClick={() => console.log("Contact board clicked")}
        />
      </div>

      {/* Hero Section - Enhanced mobile responsiveness */}
      <div className="max-w-container mx-auto px-4 py-6 md:py-12">
        <div className="flex flex-col lg:flex-row gap-6 lg:gap-16 items-center">
          <div className="flex-shrink-0 w-full max-w-sm lg:w-[361px] lg:h-[347px]">
            <img
              src="https://images.unsplash.com/photo-1552664730-d307ca884978?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"
              alt="BCDP Team Meeting"
              className="w-full h-48 sm:h-56 md:h-64 lg:h-full object-cover rounded-lg shadow-lg"
            />
          </div>
          <div className="flex-1 flex flex-col justify-center">
            <Text
              variant="body"
              weight="normal"
              as="p"
              className="text-sm sm:text-base md:text-lg text-gray-600 leading-relaxed text-left lg:text-left"
            >The Working Group 1 (WG-1) will be headed by the Secretary, Finance Division, Ministry of Finance, and its members will include the representative, Economic Relations Division, Ministry of Finance; representative, National Board of Revenue, Internal Resources Division, Ministry of Finance; representative, Bangladesh Investment Development Authority, Prime Minister's Office; representative, Ministry concerned with the project; one representative from concerned private sector (nominated by the President); Director General-III,Prime Minister's Ofce; and Additional Secretary/ Joint Secretary, Ministry of Environment, Forest and Climate Change as the Member Secretary of WG-1. The WG-1's main responsibility is to provide advice on financial resources, both public and private, that are needed to mitigate the impacts of climate change. It will also coordinate all climate financing-related matters, including financing from multilateral and bilateral partners, the private sector, and other sources; and work with the private sector to develop innovative financing instruments, such as green finance.
            </Text>
          </div>
        </div>
      </div>

      {/* Board Composition Table Section - Original design maintained with better mobile padding */}
      <div className="max-w-container mx-auto px-4 py-6">
        <Text
          variant="header"
          weight="normal"
          as="h4"
          className="text-primary-500 mb-4 md:mb-6 text-left lg:text-left text-lg sm:text-xl md:text-xl"
        >
          The Composition Of The BCDP Coordination Board Is As Follows:
        </Text>

        <div className="overflow-x-auto rounded-lg shadow-md border border-gray-100">
          <table className="min-w-full">
            <thead className="bg-gray-100">
              <tr>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
                >
                  #
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
                >
                  Positions
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
                >
                  Details
                </th>
              </tr>
            </thead>
            <tbody className="bg-white">
              {boardComposition.map((item, index) => (
                <tr
                  key={item.id}
                  className={`${
                    index % 2 === 0 ? "bg-light-green-bg" : "bg-white"
                  }`}
                >
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {item.id}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                    {item.position}
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-700">
                    {item.details}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Summary Cards Section with updated design and mobile responsiveness */}
      <div className="max-w-container mx-auto px-4 py-6 md:py-8">
        <Text
          variant="header"
          weight="normal"
          as="h2"
          className="text-primary-700 mb-6 md:mb-8 text-center lg:text-left text-xl sm:text-2xl"
        >
          Summary
        </Text>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6">
          {summaryData.map((card, index) => (
            <SummaryCard key={index} {...card} />
          ))}
        </div>
      </div>

      {/* Graph Section with mobile responsiveness */}
      <section className="py-8 md:py-16 lg:py-10 bg-white">
        <div className="max-w-container mx-auto px-4">
          <Graph />
        </div>
      </section>
      
    </div>
  );
};

export default Technology;