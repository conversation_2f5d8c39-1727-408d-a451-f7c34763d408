import React from 'react';

// Updated summary data with realistic figures
const summaryData = [
  {
    number: "12",
    title: "Total climate projects",
    description: "Covering adaptation and mitigation initiatives",
  },
  {
    number: "08",
    title: "Projects with full and partial domestic finance",
    description: "67% of total projects funded domestically",
  },
  {
    number: "06",
    title: "Projects with full and partial external finance",
    description: "50% of projects with international funding",
  },
  {
    number: "USD 2.8B",
    title: "Total fund of the climate projects",
    description: "(Domestic: 45%, External: 55%)",
  },
];

// NAP Climate Stress Areas Data
const napClimateStressAreas = [
  {
    id: 1,
    area: "South-western coastal area and Sundarbans (SWM)",
    totalInvestment: "#############",
    ongoingInvestment: "#############"
  },
  {
    id: 1,
    area: "South-east and eastern coastal area (SEE)",
    totalInvestment: "#############",
    ongoingInvestment: "#############"
  },
  {
    id: 1,
    area: "Chattogram",
    totalInvestment: "#############",
    ongoingInvestment: "#############"
  },
  {
    id: 1,
    area: "Rivers, floodplains, and erosion-prone areas (FPE)",
    totalInvestment: "#############",
    ongoingInvestment: "#############"
  },
  {
    id: 1,
    area: "Haor and flash floods areas (HFF)",
    totalInvestment: "#############",
    ongoingInvestment: "#############"
  },
  {
    id: 1,
    area: "Drought-prone and barind areas (DBA)",
    totalInvestment: "#############",
    ongoingInvestment: "#############"
  },
  {
    id: 1,
    area: "Northern, north-western region (NNW)",
    totalInvestment: "#############",
    ongoingInvestment: "#############"
  },
  {
    id: 1,
    area: "Chalan beel and low-lying area of the north-western region (CBL)",
    totalInvestment: "#############",
    ongoingInvestment: "#############"
  },
  {
    id: 1,
    area: "Char and islands (CHI)",
    totalInvestment: "#############",
    ongoingInvestment: "#############"
  },
  {
    id: 1,
    area: "Urban areas (URB)",
    totalInvestment: "#############",
    ongoingInvestment: "#############"
  },
  {
    id: 1,
    area: "Bay of Bengal and ocean (BoB)",
    totalInvestment: "#############",
    ongoingInvestment: "#############"
  }
];

// NDC Sector Projects Data
const ndcSectorProjects = [
  {
    id: 1,
    sector: "Energy (Power, Industry, Transport)",
    estimatedFund: "USD 850 million",
    totalInvestment: "USD 980 million",
    financingGap: "USD 130 million"
  },
  {
    id: 2,
    sector: "Industrial Process and Product Use (IPPU)",
    estimatedFund: "USD 420 million",
    totalInvestment: "USD 495 million",
    financingGap: "USD 75 million"
  },
  {
    id: 3,
    sector: "Agriculture, Forestry and Other Land Use (AFOLU)",
    estimatedFund: "USD 380 million",
    totalInvestment: "USD 450 million",
    financingGap: "USD 70 million"
  },
  {
    id: 4,
    sector: "Waste",
    estimatedFund: "USD 220 million",
    totalInvestment: "USD 275 million",
    financingGap: "USD 55 million"
  }
];

const Technology = () => {
  return (
    <div className="min-h-screen bg-white">
    

      {/* NAP Sector Projects Table */}
      <div className="max-w-7xl mx-auto px-4">
        <span className=" mb-6 inline-block bg-green-700 text-white px-4 py-3 text-2xl rounded-tr-2xl rounded-bl-lg font-semibold shadow-md">
          Table
        </span>
        <div className="mb-4 flex justify-between items-center">
          <h4 className="text-green-600 text-lg sm:text-xl md:text-xl font-medium">
            NAP Investment By Climate Stress Area
          </h4>
          <button className="text-green-600 hover:text-green-700 text-sm font-medium underline">
            See All
          </button>
        </div>

        <div className="overflow-x-auto rounded-lg shadow-md border border-gray-100 mb-8">
          <table className="min-w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                  # Climate Stress Area
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                  Total Investment (completed and ongoing)
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                  Ongoing Investment
                </th>
              </tr>
            </thead>
            <tbody className="bg-white">
              {napClimateStressAreas.map((item, index) => (
                <tr
                  key={index}
                  className={`${
                    index % 2 === 0 ? "bg-green-50" : "bg-white"
                  }`}
                >
                  <td className="px-6 py-4 text-sm text-gray-700">
                    {item.id} {item.area}
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-700">
                    {item.totalInvestment}
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-700">
                    {item.ongoingInvestment}
                  </td>
                </tr>
              ))}
              <tr className="bg-green-200 font-semibold">
                <td className="px-6 py-4 text-sm text-gray-900">
                  Total
                </td>
                <td className="px-6 py-4 text-sm text-gray-900">
                  #############
                </td>
                <td className="px-6 py-4 text-sm text-gray-900">
                  #############
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        {/* Remove NDC Table - only showing NAP table as per image */}
      </div>
    </div>
  );
};

export default Technology;