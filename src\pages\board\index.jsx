import { Text } from "../../components";
import PageHeader from "../../components/PageHeader";
import WorkingGroup from "./WorkingGroup";

// Data for the board composition table (unchanged)
const boardComposition = [
  {
    id: 1,
    position: "Chair",
    details:
      "Honorable Principal Secretary to the Chief Advisor's Office/Prime Minister's Office",
  },
  {
    id: 2,
    position: "Member",
    details: "Secretary, Planning Division, Ministry of Planning",
  },
  {
    id: 3,
    position: "Member",
    details: "Secretary, Finance Division, Ministry of Finance",
  },
  {
    id: 4,
    position: "Member",
    details: "Secretary, Economic Relations Division, Ministry of Finance",
  },
  {
    id: 5,
    position: "Member",
    details:
      "Secretary, Implementation, Monitoring and Evaluation Division, Ministry of Planning",
  },
  {
    id: 6,
    position: "Member",
    details:
      "Executive Chairman, Bangladesh Investment Development Authority (BIDA), Prime Minister's Office",
  },
  {
    id: 7,
    position: "Member",
    details: "Secretary, Ministry of Disaster Management and Relief",
  },
  {
    id: 8,
    position: "Member",
    details:
      "Secretary, Local Government Division, Ministry of Local Government, Rural Development and Co-operatives",
  },
  {
    id: 9,
    position: "Member",
    details: "Secretary, Ministry of Water Resources",
  },
  { id: 10, position: "Member", details: "Secretary, Ministry of Agriculture" },
  {
    id: 11,
    position: "Member",
    details:
      "One (1) Representative of Private Sector (nominated by the Chairperson)",
  },
  {
    id: 12,
    position: "Member",
    details: "Two (2) Representatives from Development Partners",
  },
  {
    id: 13,
    position: "Member",
    details: "Secretary, Ministry of Environment, Forest and Climate Change",
  },
];

// Data for the summary cards (unchanged)
const summaryData = [
  {
    title: "Total Projects",
    value: "1325",
    description: "Projects as of now",
  },
  {
    title: "On Going Projects",
    value: "743",
    description: "Projects as of now",
  },
  {
    title: "Total Commitments",
    value: "49,879",
    description: "Million USD",
  },
  {
    title: "Total Disbursements",
    value: "30,346",
    description: "Million USD",
  },
];

// SummaryCard component with updated styles (unchanged)
const SummaryCard = ({ title, value, description }) => (
  <div className="bg-breadcrumb-bg p-4 rounded-lg shadow-md border border-gray-100 flex flex-col justify-between h-full">
    <div className="flex items-center mb-2">
      <div className="h-6 w-1 bg-primary-500 rounded-full mr-2"></div>
      <h3 className="text-gray-800 font-semibold text-base">{title}</h3>
    </div>
    <p className="text-2xl font-bold text-gray-700 mb-1">{value}</p>
    <p className="text-sm text-gray-600">{description}</p>
  </div>
);

const Board = () => {
  // Define breadcrumb items (unchanged)
  const breadcrumbs = [
    { label: "Home", path: "/" },
    { label: "Board" }, // Current page, no path needed
  ];

  return (
    <div className="min-h-screen bg-white">
      {/* Mobile Header - Only shows on mobile */}
      <div className="block md:hidden px-3 py-4 bg-breadcrumb-bg">
        {/* Mobile Breadcrumbs */}
        <nav className="mb-3">
          <div className="flex items-center space-x-1 text-xs text-gray-600">
            <a href="/" className="hover:text-gray-800 transition-colors">
              Home
            </a>
            <span>/</span>
            <span className="text-gray-800 font-medium">BCDP Board</span>
          </div>
        </nav>

        {/* Mobile Title */}
        <div className="mb-4">
          <h1 className="text-lg font-bold text-gray-900 leading-tight">
            BCDP Board
          </h1>
        </div>

        {/* Mobile Contact Button */}
        {/* <button 
          onClick={() => console.log("Contact board clicked")}
          className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg font-medium text-sm transition-colors duration-200"
        >
          Contact Board
        </button> */}
      </div>

      {/* Desktop Header - Only shows on desktop */}
      <div className="hidden md:block [&_nav_a]:mx-1 sm:[&_nav_a]:mx-2 md:[&_nav_a]:mx-3 [&_nav_span]:mx-1 sm:[&_nav_span]:mx-2 md:[&_nav_span]:mx-3 [&_nav>*:first-child]:ml-0">
        <div>
          <PageHeader
            title="BCDP Board"
            breadcrumbs={breadcrumbs}
            buttonText="Contact Board"
            buttonOnClick={() => console.log("Contact board clicked")}
          />
        </div>
      </div>

      <div className="max-w-container mx-auto px-4 py-12">
        <div className="flex flex-col lg:flex-row gap-6 lg:gap-16 items-center">
          <div className="flex-shrink-0 w-full max-w-sm lg:w-[361px] lg:h-[347px]">
            <img
              src="https://images.unsplash.com/photo-1552664730-d307ca884978?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"
              alt="BCDP Team Meeting"
              className="w-full h-full object-cover rounded-lg shadow-lg"
            />
          </div>
          <div className="flex-1 flex flex-col justify-center">
            <Text
              variant="header"
              weight="normal"
              as="h1"
              className="text-gray-800 mb-6"
            >
              Bangladesh Climate Development Partnership (BCDP)
            </Text>
            <Text
              variant="body"
              weight="normal"
              as="p"
              className="text-lg text-gray-600 leading-relaxed"
            >
              The Bangladesh Climate Development Partnership (BCDP) is a
              cross-sector, multistakeholder, and multi-year partnership to
              drive climate actions at scale and with urgency, to support low
              carbon and climate-resilient development essential.
            </Text>
          </div>
        </div>
      </div>

      {/* New Section for Board Composition Table (unchanged) */}
      <div className="max-w-container mx-auto px-4 py-4">
        <Text
          variant="header"
          weight="normal"
          as="h4"
          className="text-primary-500 mb-6 text-center lg:text-left"
        >
          The Composition Of The BCDP Coordination Board Is As Follows:
        </Text>

        <div className="overflow-x-auto rounded-lg shadow-md border border-gray-100">
          <table className="min-w-full">
            <thead className="bg-gray-100">
              <tr>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
                >
                  #
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
                >
                  Positions
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
                >
                  Details
                </th>
              </tr>
            </thead>
            <tbody className="bg-white">
              {boardComposition.map((item, index) => (
                <tr
                  key={item.id}
                  className={`${
                    index % 2 === 0 ? "bg-light-green-bg" : "bg-white"
                  }`}
                >
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {item.id}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                    {item.position}
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-700">
                    {item.details}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Summary Cards Section (unchanged) */}
      <div className="max-w-container mx-auto px-4 py-8">
        <Text
          variant="header"
          weight="normal"
          as="h2"
          className="text-primary-700 mb-8 text-center lg:text-left"
        >
          Summary
        </Text>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          {summaryData.map((card, index) => (
            <SummaryCard key={index} {...card} />
          ))}
        </div>
      </div>

      <div className="max-w-container mx-auto px-4 py-4">
        <Text
          variant="header"
          weight="normal"
          as="h2"
          className="text-primary-700 mb-8 text-center lg:text-left" // Used primary-700 for consistency
        >
          Working Group Summary
        </Text>
        {[...Array(4)].map((_, index) => (
          <div key={index} className="py-8">
            {" "}
            <WorkingGroup isEven={index % 2 !== 0} />
          </div>
        ))}
      </div>
    </div>
  );
};

export default Board;
