import React from 'react';

// Updated summary data with realistic figures
const summaryData = [
  {
    number: "12",
    title: "Total climate projects",
    description: "Covering adaptation and mitigation initiatives",
  },
  {
    number: "08",
    title: "Projects with full and partial domestic finance",
    description: "67% of total projects funded domestically",
  },
  {
    number: "06",
    title: "Projects with full and partial external finance",
    description: "50% of projects with international funding",
  },
  {
    number: "USD 2.8B",
    title: "Total fund of the climate projects",
    description: "(Domestic: 45%, External: 55%)",
  },
];

// Projects List Under Development by Ministries Data
const projectsUnderDevelopment = [
  {
    id: 1,
    projectName: "Project name will appeared here",
    sectorInfo: "Sector Info",
    ministries: "Ministries Name",
    potentialDonor: "Donor Name"
  },
  {
    id: 1,
    projectName: "Project name will appeared here",
    sectorInfo: "Sector Info",
    ministries: "Ministries Name",
    potentialDonor: "Donor Name"
  },
  {
    id: 1,
    projectName: "Project name will appeared here",
    sectorInfo: "Sector Info",
    ministries: "Ministries Name",
    potentialDonor: "Donor Name"
  },
  {
    id: 1,
    projectName: "Project name will appeared here",
    sectorInfo: "Sector Info",
    ministries: "Ministries Name",
    potentialDonor: "Donor Name"
  },
  {
    id: 1,
    projectName: "Project name will appeared here",
    sectorInfo: "Sector Info",
    ministries: "Ministries Name",
    potentialDonor: "Donor Name"
  },
  {
    id: 1,
    projectName: "Project name will appeared here",
    sectorInfo: "Sector Info",
    ministries: "Ministries Name",
    potentialDonor: "Donor Name"
  },
  {
    id: 1,
    projectName: "Project name will appeared here",
    sectorInfo: "Sector Info",
    ministries: "Ministries Name",
    potentialDonor: "Donor Name"
  }
];

// NDC Sector Projects Data
const ndcSectorProjects = [
  {
    id: 1,
    sector: "Energy (Power, Industry, Transport)",
    estimatedFund: "USD 850 million",
    totalInvestment: "USD 980 million",
    financingGap: "USD 130 million"
  },
  {
    id: 2,
    sector: "Industrial Process and Product Use (IPPU)",
    estimatedFund: "USD 420 million",
    totalInvestment: "USD 495 million",
    financingGap: "USD 75 million"
  },
  {
    id: 3,
    sector: "Agriculture, Forestry and Other Land Use (AFOLU)",
    estimatedFund: "USD 380 million",
    totalInvestment: "USD 450 million",
    financingGap: "USD 70 million"
  },
  {
    id: 4,
    sector: "Waste",
    estimatedFund: "USD 220 million",
    totalInvestment: "USD 275 million",
    financingGap: "USD 55 million"
  }
];

const Technology = () => {
  return (
    <div className="min-h-screen bg-white">
      

      {/* Projects List Under Development by Ministries Table */}
      <div className="max-w-7xl mx-auto px-4">
        <div className="mb-4 flex justify-between items-center">
          <h4 className="text-green-600 text-xl sm:text-xl md:text-xl font-medium">
            Projects List Under Development by Ministries
          </h4>
          <button className="text-green-600 hover:text-green-700 text-sm font-medium underline">
            See All
          </button>
        </div>

        <div className="overflow-x-auto rounded-lg shadow-md border border-gray-100 mb-8">
          <table className="min-w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                  # Potential Ongoing projects Name
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                  Sector Information
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                  Ministries
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                  Potential Donor
                </th>
              </tr>
            </thead>
            <tbody className="bg-white">
              {projectsUnderDevelopment.map((item, index) => (
                <tr
                  key={index}
                  className={`${
                    index % 2 === 0 ? "bg-green-50" : "bg-white"
                  }`}
                >
                  <td className="px-6 py-4 text-sm text-gray-700">
                    {item.id} {item.projectName}
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-700">
                    {item.sectorInfo}
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-700">
                    {item.ministries}
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-700">
                    {item.potentialDonor}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Remove NDC Table - only showing NAP table as per image */}
      </div>
    </div>
  );
};

export default Technology;