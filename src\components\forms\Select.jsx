import { useField, useFormikContext } from 'formik';
import ReactSelect from 'react-select';
import Text from '../Text';

const Select = ({ 
  label, 
  options = [],
  placeholder = 'Select...',
  isMulti = false,
  isSearchable = true,
  isClearable = true,
  isDisabled = false,
  className = '', 
  labelClassName = '',
  errorClassName = '',
  required = false,
  ...props 
}) => {
  const [field, meta] = useField(props);
  const { setFieldValue } = useFormikContext();
  const hasError = meta.touched && meta.error;

  const handleChange = (selectedOption) => {
    if (isMulti) {
      setFieldValue(props.name, selectedOption || []);
    } else {
      setFieldValue(props.name, selectedOption);
    }
  };

  const customStyles = {
    control: (provided, state) => ({
      ...provided,
      borderColor: hasError 
        ? '#ef4444' 
        : state.isFocused 
          ? '#6366f1' 
          : '#d1d5db',
      backgroundColor: hasError ? '#fef2f2' : 'white',
      boxShadow: state.isFocused 
        ? hasError 
          ? '0 0 0 2px rgba(239, 68, 68, 0.2)'
          : '0 0 0 2px rgba(99, 102, 241, 0.2)'
        : 'none',
      '&:hover': {
        borderColor: hasError ? '#ef4444' : '#9ca3af',
      },
      minHeight: '42px',
    }),
    placeholder: (provided) => ({
      ...provided,
      color: '#9ca3af',
    }),
    multiValue: (provided) => ({
      ...provided,
      backgroundColor: '#e0e7ff',
    }),
    multiValueLabel: (provided) => ({
      ...provided,
      color: '#3730a3',
    }),
    multiValueRemove: (provided) => ({
      ...provided,
      color: '#6366f1',
      '&:hover': {
        backgroundColor: '#c7d2fe',
        color: '#4338ca',
      },
    }),
  };

  return (
    <div className={`mb-4 ${className}`}>
      {label && (
        <label 
          htmlFor={props.name} 
          className={`block mb-2 ${labelClassName}`}
        >
          <Text variant="body" weight="medium" className="text-gray-700">
            {label}
            {required && <span className="text-red-500 ml-1">*</span>}
          </Text>
        </label>
      )}
      
      <ReactSelect
        {...props}
        options={options}
        value={field.value}
        onChange={handleChange}
        placeholder={placeholder}
        isMulti={isMulti}
        isSearchable={isSearchable}
        isClearable={isClearable}
        isDisabled={isDisabled}
        styles={customStyles}
        className="react-select-container"
        classNamePrefix="react-select"
      />
      
      {hasError && (
        <Text 
          variant="small" 
          className={`text-red-500 mt-1 ${errorClassName}`}
        >
          {meta.error}
        </Text>
      )}
    </div>
  );
};

export default Select;
