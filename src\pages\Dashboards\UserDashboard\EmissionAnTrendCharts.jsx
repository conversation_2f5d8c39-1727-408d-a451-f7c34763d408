import TrendChart from "../../../components/TrendChart";
import Barchart from "../../../components/Barchart";

const EmissionAndTrendCharts = ({ trendChartData, barChartData }) => {
  return (
    <div className="w-full">
      <div className="grid grid-cols-1 md:grid-cols-12 gap-6">
        {/* Emission Chart - 7/12 */}
        <div
          className="md:col-span-7 bg-white p-4 rounded-lg border border-gray-200"
          style={{
            boxShadow: "0px 0px 12.75px 3.19px rgba(0, 0, 0, 0.08)",
          }}
        >
          <Barchart title={"Total Emission"} data={barChartData} />
        </div>
        {/* Trend Chart - 5/12 */}
        <div
          className="md:col-span-5 bg-white p-4 rounded-lg border border-gray-200"
          style={{
            boxShadow: "0px 0px 12.75px 3.19px rgba(0, 0, 0, 0.08)",
          }}
        >
          <TrendChart
            header={"Tendance at 3.5 year old MCH visit"}
            data={trendChartData}
          />
        </div>
      </div>
    </div>
  );
};

export default EmissionAndTrendCharts;
