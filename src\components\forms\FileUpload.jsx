import { useField, useFormikContext } from 'formik';
import Text from '../Text';
import { useState } from 'react';

const FileUpload = ({ 
  label, 
  accept,
  multiple = false,
  maxSize = 5 * 1024 * 1024, // 5MB default
  className = '', 
  labelClassName = '',
  errorClassName = '',
  required = false,
  disabled = false,
  ...props 
}) => {
  const [field, meta] = useField(props);
  const { setFieldValue } = useFormikContext();
  const [dragActive, setDragActive] = useState(false);
  const hasError = meta.touched && meta.error;

  const handleFiles = (files) => {
    const fileArray = Array.from(files);
    
    // Validate file size
    const validFiles = fileArray.filter(file => {
      if (file.size > maxSize) {
        alert(`File ${file.name} is too large. Maximum size is ${maxSize / (1024 * 1024)}MB`);
        return false;
      }
      return true;
    });

    if (multiple) {
      setFieldValue(props.name, [...(field.value || []), ...validFiles]);
    } else {
      setFieldValue(props.name, validFiles[0] || null);
    }
  };

  const handleDrag = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFiles(e.dataTransfer.files);
    }
  };

  const handleChange = (e) => {
    if (e.target.files && e.target.files[0]) {
      handleFiles(e.target.files);
    }
  };

  const removeFile = (index) => {
    if (multiple) {
      const newFiles = field.value.filter((_, i) => i !== index);
      setFieldValue(props.name, newFiles);
    } else {
      setFieldValue(props.name, null);
    }
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className={`mb-4 ${className}`}>
      {label && (
        <label className={`block mb-2 ${labelClassName}`}>
          <Text variant="body" weight="medium" className="text-gray-700">
            {label}
            {required && <span className="text-red-500 ml-1">*</span>}
          </Text>
        </label>
      )}
      
      <div
        className={`
          relative border-2 border-dashed rounded-lg p-6 text-center transition-colors duration-200
          ${dragActive 
            ? 'border-primary-500 bg-primary-50' 
            : hasError 
              ? 'border-red-500 bg-red-50' 
              : 'border-gray-300 hover:border-gray-400'
          }
          ${disabled ? 'bg-gray-100 cursor-not-allowed' : 'cursor-pointer'}
        `}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
        onClick={() => !disabled && document.getElementById(props.name).click()}
      >
        <input
          id={props.name}
          type="file"
          accept={accept}
          multiple={multiple}
          disabled={disabled}
          onChange={handleChange}
          className="hidden"
        />
        
        <div className="space-y-2">
          <Text variant="body" className="text-gray-600">
            {dragActive 
              ? 'Drop files here...' 
              : 'Click to upload or drag and drop'
            }
          </Text>
          <Text variant="small" className="text-gray-500">
            {accept && `Accepted formats: ${accept}`}
            {maxSize && ` • Max size: ${formatFileSize(maxSize)}`}
          </Text>
        </div>
      </div>

      {/* Display selected files */}
      {field.value && (
        <div className="mt-2 space-y-2">
          {multiple ? (
            field.value.map((file, index) => (
              <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                <div>
                  <Text variant="small" weight="medium">{file.name}</Text>
                  <Text variant="small" className="text-gray-500">{formatFileSize(file.size)}</Text>
                </div>
                <button
                  type="button"
                  onClick={() => removeFile(index)}
                  className="text-red-500 hover:text-red-700"
                >
                  ×
                </button>
              </div>
            ))
          ) : (
            <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
              <div>
                <Text variant="small" weight="medium">{field.value.name}</Text>
                <Text variant="small" className="text-gray-500">{formatFileSize(field.value.size)}</Text>
              </div>
              <button
                type="button"
                onClick={() => removeFile()}
                className="text-red-500 hover:text-red-700"
              >
                ×
              </button>
            </div>
          )}
        </div>
      )}
      
      {hasError && (
        <Text 
          variant="small" 
          className={`text-red-500 mt-1 ${errorClassName}`}
        >
          {meta.error}
        </Text>
      )}
    </div>
  );
};

export default FileUpload;
