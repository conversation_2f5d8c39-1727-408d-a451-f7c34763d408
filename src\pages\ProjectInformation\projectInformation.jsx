import React, { useState } from 'react';
import { Icon } from "@iconify/react";
import * as Yup from 'yup';
import {
  Form,
  InputField,
  TextArea,
  Select,
  FileUpload,
  Button
} from '../../components/forms';
import Text from '../../components/Text';
import PageHeader from "../../components/PageHeader";

// Step indicator component
const StepIndicator = ({ steps, currentStep, stepErrors }) => {
  return (
    <div className="flex items-center justify-center mb-8">
      {steps.map((step, index) => {
        const hasError = stepErrors[index];
        const isCompleted = index < currentStep;
        const isCurrent = index === currentStep;

        return (
          <div key={index} className="flex items-center">
            <div className="flex items-center">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                hasError
                  ? 'bg-red-500 text-white'
                  : isCompleted
                  ? 'bg-green-500 text-white'
                  : isCurrent
                  ? 'bg-green-500 text-white'
                  : 'bg-gray-200 text-gray-600'
              }`}>
                {hasError ? (
                  <Icon icon="mdi:alert" width={16} height={16} />
                ) : isCompleted ? (
                  <Icon icon="mdi:check" width={16} height={16} />
                ) : (
                  String(index + 1).padStart(2, '0')
                )}
              </div>
              <span className={`ml-2 text-sm font-medium ${
                hasError
                  ? 'text-red-600'
                  : index <= currentStep
                  ? 'text-green-600'
                  : 'text-gray-400'
              }`}>
                {step}
              </span>
            </div>
            {index < steps.length - 1 && (
              <div className={`w-12 h-0.5 mx-4 ${
                hasError
                  ? 'bg-red-500'
                  : index < currentStep
                  ? 'bg-green-500'
                  : 'bg-gray-200'
              }`} />
            )}
          </div>
        );
      })}
    </div>
  );
};

// Validation schema for the multi-step form
const validationSchema = Yup.object({
  // Step 1: Project Information
  projectTitle: Yup.string().required('Project title is required'),
  executingAgencies: Yup.string().required('Executing agencies is required'),
  agencyType: Yup.object().nullable().required('Agency type is required'),
  performance: Yup.object().nullable(),
  projectStatus: Yup.object().nullable().required('Project status is required'),
  sector: Yup.object().nullable().required('Sector is required'),
  selectSector: Yup.object().nullable(),
  climateStressedArea: Yup.object().nullable(),
  startYear: Yup.object().nullable().required('Start year is required'),
  endYear: Yup.object().nullable().required('End year is required'),
  projectType: Yup.object().nullable().required('Project type is required'),
  district: Yup.object().nullable().required('District is required'),
  projectConcerns: Yup.string(),
  contactNumber: Yup.string().required('Contact number is required'),
  uploadedFiles: Yup.mixed().nullable(),

  // Step 2: Project Funding
  fundingType: Yup.object().nullable().required('Funding type is required'),
  totalAllocatedFund: Yup.string().required('Total allocated fund is required'),
  domesticFund: Yup.string(),
  externalFund: Yup.string(),
  completedFund: Yup.string(),
  ongoingFund: Yup.string(),
  donorName: Yup.string(),
  fundSpent: Yup.string(),
  fundStatus: Yup.object().nullable().required('Fund status is required'),
  createdDate: Yup.string().required('Created date is required'),
  updatedDate: Yup.string(),

  // Step 3: Project Performance
  quarter: Yup.object().nullable().required('Quarter is required'),
  year: Yup.object().nullable().required('Year is required'),
  plannedFundSpent: Yup.string().required('Planned fund spent is required'),
  actualFundSpent: Yup.string().required('Actual fund spent is required'),
  plannedWorkCompletion: Yup.string().required('Planned work completion is required'),
  actualWorkCompletion: Yup.string().required('Actual work completion is required'),
  projectStatusPerf: Yup.object().nullable().required('Project status is required'),

  // Step 4: Policy Knowledge Capacity
  componentType: Yup.object().nullable().required('Component type is required'),
  thematicArea: Yup.object().nullable().required('Thematic area is required'),
  hrDevelopment: Yup.string().required('HR Development is required'),
  institutionActivityCount1: Yup.string().required('Institution activity count is required'),
  institutionActivityCount2: Yup.string(),
  impactActivityCount: Yup.string().required('Impact activity count is required'),
  activityDescriptions: Yup.string().required('Activity descriptions is required')
});

// Step-wise validation schemas
const stepValidationSchemas = {
  0: Yup.object({
    projectTitle: Yup.string().required('Project title is required'),
    executingAgencies: Yup.string().required('Executing agencies is required'),
    agencyType: Yup.object().nullable().required('Agency type is required'),
    projectStatus: Yup.object().nullable().required('Project status is required'),
    sector: Yup.object().nullable().required('Sector is required'),
    startYear: Yup.object().nullable().required('Start year is required'),
    endYear: Yup.object().nullable().required('End year is required'),
    projectType: Yup.object().nullable().required('Project type is required'),
    district: Yup.object().nullable().required('District is required'),
    contactNumber: Yup.string().required('Contact number is required'),
  }),
  1: Yup.object({
    fundingType: Yup.object().nullable().required('Funding type is required'),
    totalAllocatedFund: Yup.string().required('Total allocated fund is required'),
    fundStatus: Yup.object().nullable().required('Fund status is required'),
    createdDate: Yup.string().required('Created date is required'),
  }),
  2: Yup.object({
    quarter: Yup.object().nullable().required('Quarter is required'),
    year: Yup.object().nullable().required('Year is required'),
    plannedFundSpent: Yup.string().required('Planned fund spent is required'),
    actualFundSpent: Yup.string().required('Actual fund spent is required'),
    plannedWorkCompletion: Yup.string().required('Planned work completion is required'),
    actualWorkCompletion: Yup.string().required('Actual work completion is required'),
    projectStatusPerf: Yup.object().nullable().required('Project status is required'),
  }),
  3: Yup.object({
    componentType: Yup.object().nullable().required('Component type is required'),
    thematicArea: Yup.object().nullable().required('Thematic area is required'),
    hrDevelopment: Yup.string().required('HR Development is required'),
    institutionActivityCount1: Yup.string().required('Institution activity count is required'),
    impactActivityCount: Yup.string().required('Impact activity count is required'),
    activityDescriptions: Yup.string().required('Activity descriptions is required')
  })
};

// Initial form values
const initialValues = {
  // Step 1: Project Information
  projectTitle: '',
  executingAgencies: '',
  agencyType: null,
  performance: null,
  projectStatus: null,
  sector: null,
  selectSector: null,
  climateStressedArea: null,
  startYear: null,
  endYear: null,
  projectType: null,
  district: null,
  projectConcerns: '',
  contactNumber: '',
  uploadedFiles: null,

  // Step 2: Project Funding
  fundingType: null,
  totalAllocatedFund: '',
  domesticFund: '',
  externalFund: '',
  completedFund: '',
  ongoingFund: '',
  donorName: '',
  fundSpent: '',
  fundStatus: null,
  createdDate: '',
  updatedDate: '',

  // Step 3: Project Performance
  quarter: null,
  year: null,
  plannedFundSpent: '',
  actualFundSpent: '',
  plannedWorkCompletion: '',
  actualWorkCompletion: '',
  projectStatusPerf: null,

  // Step 4: Policy Knowledge Capacity
  componentType: null,
  thematicArea: null,
  hrDevelopment: '',
  institutionActivityCount1: '',
  institutionActivityCount2: '',
  impactActivityCount: '',
  activityDescriptions: ''
};

const ProjectInformation = () => {
  const [currentStep, setCurrentStep] = useState(0);
  const [stepErrors, setStepErrors] = useState({
    0: false,
    1: false,
    2: false,
    3: false
  });

  const steps = [
    'PROJECT INFORMATION',
    'PROJECT FUNDING',
    'PROJECT PERFORMANCE',
    'POLICY KNOWLEDGE CAPACITY'
  ];

  // Options for dropdowns
  const agencyTypeOptions = [
    { value: 'government', label: 'Government' },
    { value: 'private', label: 'Private' },
    { value: 'ngo', label: 'NGO' },
    { value: 'international', label: 'International' }
  ];

  const performanceOptions = [
    { value: 'excellent', label: 'Excellent' },
    { value: 'good', label: 'Good' },
    { value: 'average', label: 'Average' },
    { value: 'poor', label: 'Poor' }
  ];

  const projectStatusOptions = [
    { value: 'active', label: 'Active' },
    { value: 'completed', label: 'Completed' },
    { value: 'on_hold', label: 'On Hold' },
    { value: 'cancelled', label: 'Cancelled' }
  ];

  const sectorOptions = [
    { value: 'health', label: 'Health' },
    { value: 'education', label: 'Education' },
    { value: 'infrastructure', label: 'Infrastructure' },
    { value: 'agriculture', label: 'Agriculture' }
  ];

  const selectSectorOptions = [
    { value: 'primary', label: 'Primary' },
    { value: 'secondary', label: 'Secondary' },
    { value: 'tertiary', label: 'Tertiary' }
  ];

  const climateStressedAreaOptions = [
    { value: 'coastal', label: 'Coastal' },
    { value: 'drought_prone', label: 'Drought Prone' },
    { value: 'flood_prone', label: 'Flood Prone' },
    { value: 'cyclone_prone', label: 'Cyclone Prone' }
  ];

  const yearOptions = [
    { value: '2020', label: '2020' },
    { value: '2021', label: '2021' },
    { value: '2022', label: '2022' },
    { value: '2023', label: '2023' },
    { value: '2024', label: '2024' },
    { value: '2025', label: '2025' },
    { value: '2026', label: '2026' },
    { value: '2027', label: '2027' },
    { value: '2028', label: '2028' },
    { value: '2029', label: '2029' },
    { value: '2030', label: '2030' }
  ];

  const projectTypeOptions = [
    { value: 'development', label: 'Development' },
    { value: 'research', label: 'Research' },
    { value: 'infrastructure', label: 'Infrastructure' },
    { value: 'capacity_building', label: 'Capacity Building' }
  ];

  const districtOptions = [
    { value: 'dhaka', label: 'Dhaka' },
    { value: 'chittagong', label: 'Chittagong' },
    { value: 'sylhet', label: 'Sylhet' },
    { value: 'rajshahi', label: 'Rajshahi' },
    { value: 'khulna', label: 'Khulna' }
  ];

  // Validate current step
  const validateCurrentStep = async (values) => {
    try {
      await stepValidationSchemas[currentStep].validate(values, { abortEarly: false });
      return true;
    } catch (error) {
      return false;
    }
  };

  // Check if all steps are valid
  const validateAllSteps = async (values) => {
    const errors = { ...stepErrors };

    for (let step = 0; step < steps.length; step++) {
      try {
        await stepValidationSchemas[step].validate(values, { abortEarly: false });
        errors[step] = false;
      } catch (error) {
        errors[step] = true;
      }
    }

    setStepErrors(errors);
    return !Object.values(errors).some(hasError => hasError);
  };

  const nextStep = async (values) => {
    const isCurrentStepValid = await validateCurrentStep(values);

    if (!isCurrentStepValid) {
      // Mark current step as having errors
      setStepErrors(prev => ({ ...prev, [currentStep]: true }));
      return;
    }

    // Clear error for current step
    setStepErrors(prev => ({ ...prev, [currentStep]: false }));

    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmit = async (values) => {
    const allStepsValid = await validateAllSteps(values);

    if (allStepsValid) {
      console.log('Form submitted:', values);
      alert('Project created successfully!');
    } else {
      alert('Please fill in all required fields in all steps before submitting.');
    }
  };

  const renderStep = () => {
    switch (currentStep) {
      case 0:
        return (
          <div className="space-y-6">
            <Text variant="heading" className="text-center mb-8">Project Information</Text>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <InputField
                name="projectTitle"
                label="Project Title"
                placeholder="Enter project title"
                required
              />
              <InputField
                name="executingAgencies"
                label="Executing Agencies"
                placeholder="Enter executing agencies"
                required
              />
              <Select
                name="agencyType"
                label="Agency Type"
                placeholder="Select agency type"
                options={agencyTypeOptions}
                required
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Select
                name="performance"
                label="Performance"
                placeholder="Select Performance"
                options={performanceOptions}
              />
              <Select
                name="projectStatus"
                label="Project Status"
                placeholder="Select Project Status"
                options={projectStatusOptions}
                required
              />
              <Select
                name="sector"
                label="Sector"
                placeholder="Select sector"
                options={sectorOptions}
                required
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Select
                name="selectSector"
                label="Select Sector"
                placeholder="Select Sector"
                options={selectSectorOptions}
              />
              <Select
                name="climateStressedArea"
                label="Climate Stressed Area"
                placeholder="Select climate stressed area"
                options={climateStressedAreaOptions}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Select
                name="startYear"
                label="Start Year"
                placeholder="Select start year"
                options={yearOptions}
                required
              />
              <Select
                name="endYear"
                label="End Year"
                placeholder="Select end year"
                options={yearOptions}
                required
              />
              <Select
                name="projectType"
                label="Project Type"
                placeholder="Select project type"
                options={projectTypeOptions}
                required
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Select
                name="district"
                label="District"
                placeholder="Select district"
                options={districtOptions}
                required
              />
              <InputField
                name="projectConcerns"
                label="Project Concerns"
                placeholder="Enter project concerns"
              />
              <InputField
                name="contactNumber"
                label="Contact Number"
                placeholder="Enter contact number"
                required
              />
            </div>

            <FileUpload
              name="uploadedFiles"
              label="Upload Files"
              accept=".doc,.docx,.pdf,.xls,.xlsx,.png,.jpg,.jpeg"
              maxSize={10 * 1024 * 1024} // 10MB
            />
          </div>
        );

      case 1:
        return (
          <div className="space-y-6">
            <Text variant="heading" className="text-center mb-8">Project Funding</Text>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Select
                name="fundingType"
                label="Funding Type"
                placeholder="Select funding type"
                options={[
                  { value: 'government', label: 'Government' },
                  { value: 'international', label: 'International' },
                  { value: 'private', label: 'Private' },
                  { value: 'mixed', label: 'Mixed' }
                ]}
                required
              />
              <InputField
                name="totalAllocatedFund"
                label="Total Allocated Fund"
                placeholder="Enter total allocated fund"
                type="number"
                required
              />
              <InputField
                name="domesticFund"
                label="Domestic Fund"
                placeholder="Enter domestic fund"
                type="number"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <InputField
                name="externalFund"
                label="External Fund"
                placeholder="Enter external fund"
                type="number"
              />
              <InputField
                name="completedFund"
                label="Completed Fund"
                placeholder="Enter completed fund"
                type="number"
              />
              <InputField
                name="ongoingFund"
                label="Ongoing Fund"
                placeholder="Enter ongoing fund"
                type="number"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <InputField
                name="donorName"
                label="Donor Name"
                placeholder="Enter donor name"
              />
              <InputField
                name="fundSpent"
                label="Fund Spent"
                placeholder="Enter fund spent"
                type="number"
              />
              <Select
                name="fundStatus"
                label="Fund Status"
                placeholder="Select Fund Status"
                options={[
                  { value: 'active', label: 'Active' },
                  { value: 'completed', label: 'Completed' },
                  { value: 'pending', label: 'Pending' },
                  { value: 'cancelled', label: 'Cancelled' }
                ]}
                required
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <InputField
                name="createdDate"
                label="Created Date"
                placeholder="Enter Created Date"
                type="date"
                required
              />
              <InputField
                name="updatedDate"
                label="Updated Date"
                placeholder="Enter updated date"
                type="date"
              />
            </div>
          </div>
        );

      case 2:
        return (
          <div className="space-y-6">
            <Text variant="heading" className="text-center mb-8">Project Performance</Text>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Select
                name="quarter"
                label="Quarter"
                placeholder="Select quarter"
                options={[
                  { value: 'q1', label: 'Q1' },
                  { value: 'q2', label: 'Q2' },
                  { value: 'q3', label: 'Q3' },
                  { value: 'q4', label: 'Q4' }
                ]}
                required
              />
              <Select
                name="year"
                label="Year"
                placeholder="Select year"
                options={[
                  { value: '2022', label: '2022' },
                  { value: '2023', label: '2023' },
                  { value: '2024', label: '2024' },
                  { value: '2025', label: '2025' }
                ]}
                required
              />
              <InputField
                name="plannedFundSpent"
                label="Planned Fund Spent"
                placeholder="Enter planned fund spent"
                type="number"
                required
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <InputField
                name="actualFundSpent"
                label="Actual Fund Spent"
                placeholder="Enter actual fund spent"
                type="number"
                required
              />
              <InputField
                name="plannedWorkCompletion"
                label="Planned Work Completion"
                placeholder="Enter planned work completion rate"
                type="number"
                required
              />
              <InputField
                name="actualWorkCompletion"
                label="Actual Work Completion"
                placeholder="Enter actual work completion"
                type="number"
                required
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-1 gap-6">
              <Select
                name="projectStatusPerf"
                label="Project Status"
                placeholder="Select project status"
                options={[
                  { value: 'on_track', label: 'On Track' },
                  { value: 'behind_schedule', label: 'Behind Schedule' },
                  { value: 'ahead_of_schedule', label: 'Ahead of Schedule' },
                  { value: 'at_risk', label: 'At Risk' }
                ]}
                required
              />
            </div>
          </div>
        );

      case 3:
        return (
          <div className="space-y-6">
            <Text variant="heading" className="text-center mb-8">Policy Knowledge Capacity</Text>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Select
                name="componentType"
                label="Component Type"
                placeholder="Select component type"
                options={[
                  { value: 'policy_development', label: 'Policy Development' },
                  { value: 'capacity_building', label: 'Capacity Building' },
                  { value: 'knowledge_management', label: 'Knowledge Management' },
                  { value: 'research', label: 'Research' }
                ]}
                required
              />
              <Select
                name="thematicArea"
                label="Thematic Area"
                placeholder="Select thematic area"
                options={[
                  { value: 'climate_change', label: 'Climate Change' },
                  { value: 'disaster_management', label: 'Disaster Management' },
                  { value: 'sustainable_development', label: 'Sustainable Development' },
                  { value: 'governance', label: 'Governance' }
                ]}
                required
              />
              <InputField
                name="hrDevelopment"
                label="HR Development"
                placeholder="Enter HR Development"
                required
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <InputField
                name="institutionActivityCount1"
                label="Institution Activity Count 1"
                placeholder="Enter institution activity count"
                type="number"
                required
              />
              <InputField
                name="institutionActivityCount2"
                label="Institution Activity Count 2"
                placeholder="Enter institution activity count"
                type="number"
              />
              <InputField
                name="impactActivityCount"
                label="Impact Activity Count"
                placeholder="Enter impact activity count"
                type="number"
                required
              />
            </div>

            <div className="grid grid-cols-1 gap-6">
              <TextArea
                name="activityDescriptions"
                label="Activity Descriptions"
                placeholder="Enter activity descriptions"
                rows={4}
                required
              />
            </div>
          </div>
        );

      default:
        return null;
    }
    
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-8">
          <Text variant="heading" className="text-3xl font-bold text-gray-900 mb-4">
            Create Project
          </Text>
        </div>

        {/* Step Indicator */}
        <StepIndicator steps={steps} currentStep={currentStep} stepErrors={stepErrors} />

        {/* Form Content */}
        <Form
          initialValues={initialValues}
          validationSchema={validationSchema}
          onSubmit={handleSubmit}
          showActions={false}
          enableReinitialize={true}
        >
          {(formikProps) => (
            <>
              <div className="bg-white rounded-lg shadow-lg p-8 mb-8">
                {renderStep()}
              </div>

              {/* Navigation Buttons */}
              <div className="flex justify-between">
                <Button
                  type="button"
                  onClick={prevStep}
                  disabled={currentStep === 0}
                  variant={currentStep === 0 ? "outline" : "secondary"}
                  className={currentStep === 0 ? "opacity-50 cursor-not-allowed" : ""}
                >
                  Previous
                </Button>

                {currentStep === steps.length - 1 ? (
                  <Button
                    type="button"
                    onClick={() => handleSubmit(formikProps.values)}
                    variant="primary"
                    disabled={Object.values(stepErrors).some(hasError => hasError)}
                    className={`bg-green-600 hover:bg-green-700 focus:ring-green-500 ${
                      Object.values(stepErrors).some(hasError => hasError)
                        ? 'opacity-50 cursor-not-allowed'
                        : ''
                    }`}
                  >
                    Submit
                  </Button>
                ) : (
                  <Button
                    type="button"
                    onClick={() => nextStep(formikProps.values)}
                    variant="primary"
                    className="bg-green-600 hover:bg-green-700 focus:ring-green-500"
                  >
                    Save & Next
                  </Button>
                )}
              </div>
            </>
          )}
        </Form>
      </div>
    </div>
  );
};

export default ProjectInformation;