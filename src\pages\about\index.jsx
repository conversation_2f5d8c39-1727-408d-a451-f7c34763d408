import React from "react";
import Text from "../../components/Text";
import organizationImage from "../../assets/organization.png";
import { Icon } from "@iconify/react";

const officials = [
  {
    name: "Dr. <PERSON>",
    title: "Director General (Additional Secretary)",
    department: "Department of Environment",
    email: "<EMAIL>",
    phone: "+995-940-555-766",
    image:
      "https://images.unsplash.com/photo-1603415526960-f8f8d75b9d1d?auto=format&fit=crop&w=400&q=80",
  },
  {
    name: "<PERSON><PERSON><PERSON>",
    title: "Deputy Secretary",
    department: "Climate Policy Division",
    email: "<EMAIL>",
    phone: "+880-1700-123-456",
    image:
      "https://images.unsplash.com/photo-1544005313-94ddf0286df2?auto=format&fit=crop&w=400&q=80",
  },
  {
    name: "<PERSON><PERSON><PERSON> <PERSON>",
    title: "Senior Climate Analyst",
    department: "MOEFCC",
    email: "<EMAIL>",
    phone: "+880-1900-654-321",
    image:
      "https://images.unsplash.com/photo-1502767089025-6572583495b4?auto=format&fit=crop&w=400&q=80",
  },
  {
    name: "Tahmina Sultana",
    title: "Program Coordinator",
    department: "Climate Adaptation Unit",
    email: "<EMAIL>",
    phone: "+880-1600-777-999",
    image:
      "https://images.unsplash.com/photo-1529626455594-4ff0802cfb7e?auto=format&fit=crop&w=400&q=80",
  },
  {
    name: "Anisur Rahman",
    title: "Technical Adviser",
    department: "GHG Monitoring Cell",
    email: "<EMAIL>",
    phone: "+880-1500-444-222",
    image:
      "https://images.unsplash.com/photo-1544716278-ca5e3f4abd8c?auto=format&fit=crop&w=400&q=80",
  },
  {
    name: "Shamima Akter",
    title: "Policy Analyst",
    department: "Environmental Research Council",
    email: "<EMAIL>",
    phone: "+880-1800-888-333",
    image:
      "https://images.unsplash.com/photo-1607746882042-944635dfe10e?auto=format&fit=crop&w=400&q=80",
  },
  {
    name: "Hasan Mahmud",
    title: "Chief Environmental Officer",
    department: "National Planning Commission",
    email: "<EMAIL>",
    phone: "+880-1800-111-333",
    image:
      "https://images.unsplash.com/photo-1614289378946-eda5dd91f03f?auto=format&fit=crop&w=400&q=80",
  },
  {
    name: "Rokeya Begum",
    title: "Senior Policy Coordinator",
    department: "Sustainable Development Unit",
    email: "<EMAIL>",
    phone: "+880-1700-222-555",
    image:
      "https://images.unsplash.com/photo-1582721985253-1b6fae1c87f4?auto=format&fit=crop&w=400&q=80",
  },
];

const videos = [
  {
    id: 1,
    image:
      "https://images.unsplash.com/photo-1614064642307-996eb63fe1b2?auto=format&fit=crop&w=800&q=80",
    title: "Message About: Climate change",
    date: "September 24, 2017",
  },
  {
    id: 2,
    image:
      "https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?auto=format&fit=crop&w=800&q=80",
    title: "Environmental Strategy Briefing",
    date: "October 12, 2019",
  },
  {
    id: 3,
    image:
      "https://images.unsplash.com/photo-1611974789855-9c4bfe7bfe49?auto=format&fit=crop&w=800&q=80",
    title: "Policy Announcement: Green Goals",
    date: "March 3, 2021",
  },
];

const VideoCard = ({ image, title, date }) => (
  <div className="relative group w-full h-48 sm:h-60 rounded-xl overflow-hidden shadow-md">
    <img
      src={image}
      alt={title}
      className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
    />

    {/* Play Button Overlay */}
    <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30">
      <div className="w-10 h-10 sm:w-12 sm:h-12 bg-white rounded-full flex items-center justify-center shadow">
        <Icon icon="mdi:play" className="text-gray-800 w-5 h-5 sm:w-6 sm:h-6" />
      </div>
    </div>

    {/* Hover Info Panel */}
    <div className="absolute bottom-0 left-0 right-0 bg-white bg-opacity-70 px-3 py-2 sm:px-4 sm:py-3 opacity-0 group-hover:opacity-100 transition-all duration-300 border-l-[4px] sm:border-l-[6px] border-transparent group-hover:border-primary-700">
      <p className="text-xs sm:text-sm font-medium text-gray-800 line-clamp-2">{title}</p>
      <p className="text-xs text-gray-500">{date}</p>
    </div>
  </div>
);

const OfficialCard = ({ name, title, department, email, phone, image }) => (
  <div className="bg-white border border-[#EAECF0] rounded-[10px] p-3 sm:p-4 w-full max-w-[300px] shadow-[0_0_4px_rgba(0,0,0,0.12)]">
    <img
      src={image}
      alt={name}
      className="w-full h-40 sm:h-52 object-cover rounded-lg mb-3"
    />
    <h3 className="text-base sm:text-lg font-semibold text-gray-800 mb-1 line-clamp-2">{name}</h3>
    <p className="text-xs sm:text-sm text-gray-600 mb-4 sm:mb-5 break-words line-clamp-3">
      {title}&nbsp;,&nbsp;{department}
    </p>

    <div className="mt-4 sm:mt-5 space-y-2 sm:space-y-3">
      <div className="flex items-start text-xs sm:text-sm text-gray-700">
        <div className="w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center rounded-full bg-green-100 mr-2 sm:mr-3 flex-shrink-0">
          <Icon icon="mdi:email-outline" className="w-3 h-3 sm:w-4 sm:h-4 text-green-700" />
        </div>
        <span className="break-all leading-relaxed">{email}</span>
      </div>
      <div className="flex items-center text-xs sm:text-sm text-gray-700">
        <div className="w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center rounded-full bg-green-100 mr-2 sm:mr-3 flex-shrink-0">
          <Icon icon="mdi:phone-outline" className="w-3 h-3 sm:w-4 sm:h-4 text-green-700" />
        </div>
        <span>{phone}</span>
      </div>
    </div>
  </div>
);

const About = () => {
  return (
    <div className="min-h-screen">
      <div className="bg-white">
        <div className="max-w-container mx-auto px-4 py-8 sm:py-12 lg:py-16">
          <div className="flex flex-col lg:flex-row gap-6 lg:gap-16 items-center">
            <div className="flex-shrink-0 w-full max-w-sm lg:w-[361px] lg:h-[347px]">
              <img
                src="https://images.unsplash.com/photo-1552664730-d307ca884978?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"
                alt="BCDP Team Meeting"
                className="w-full h-48 sm:h-64 lg:h-full object-cover rounded-lg shadow-lg"
              />
            </div>
            <div className="flex-1 flex flex-col justify-center">
              <Text
                variant="header"
                weight="normal"
                as="h1"
                className="text-gray-800 mb-4 sm:mb-6 text-xl sm:text-2xl lg:text-3xl text-center lg:text-left"
              >
                Bangladesh Climate Development Partnership (BCDP)
              </Text>
              <Text
                variant="body"
                weight="normal"
                as="p"
                className="text-base sm:text-lg text-gray-600 leading-relaxed text-left"
              >
                The Bangladesh Climate Development Partnership (BCDP) is a
                cross-sector, multistakeholder, and multi-year partnership to
                drive climate actions at scale and with urgency, to support low
                carbon and climate-resilient development essential.
              </Text>
            </div>
          </div>
        </div>
      </div>

      <div className="flex justify-center px-4 py-8 sm:py-12 lg:py-16">
        <div className="bg-light-green-bg max-w-container w-full px-4 sm:px-6 lg:px-8 py-6 sm:py-8 lg:py-12 rounded-lg">
          <div className="flex flex-col lg:flex-row gap-6 lg:gap-12 items-center">
            <div className="lg:w-[55%] flex flex-col justify-center">
              <Text
                variant="header"
                weight="normal"
                as="h2"
                className="text-gray-800 mb-4 sm:mb-6 text-xl sm:text-2xl lg:text-3xl text-center lg:text-left"
              >
                Who We Are
              </Text>
              <Text
                variant="secondary-header"
                weight="light"
                as="p"
                className="text-sm sm:text-base lg:text-lg text-gray-600 leading-relaxed text-left lg:text-justify"
              >
                Climate change is already a key threat to sustainable
                development. Bangladesh is one of the most vulnerable countries
                in the world with high levels of exposure to a range of
                climate-related hazards including floods, tropical cyclones,
                heat waves, and drought. The socioeconomic impacts of
                climate-related disasters are already being felt across the
                country. Such impacts are expected to grow by manifolds in the
                future due to changes in precipitation patterns with likely
                unprecedented extremes, increase in temperature, and sea level
                rise due to climate change. Further, rapid economic growth in
                the past decades has resulted in an increase in greenhouse gas
                emissions which is contributing to development challenges
                related to health from poor air quality and water quality to
                increasing trafic congestion. Estimates suggest that the effects
                of climate change could cause an average loss of about 1.3% of
                the gross domestic product per year until 2041, challenging the
                achievement of the country's long-term development goal.
                Government attaches highest priority to climate change
              </Text>
            </div>
            <div className="lg:w-[45%] flex-shrink-0 w-full max-w-md lg:max-w-none lg:h-[400px]">
              <img
                src="https://images.unsplash.com/photo-**********-cf412ec27db2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"
                alt="Climate development"
                className="w-full h-48 sm:h-64 lg:h-full object-cover rounded-lg shadow-lg"
              />
            </div>
          </div>
        </div>
      </div>

      <div className="">
        <div className="max-w-container mx-auto px-4 py-8 sm:py-12 lg:py-16">
          <div className="flex flex-col lg:flex-row gap-6 lg:gap-16 items-start">
            <div
              className="flex-shrink-0 w-full lg:w-[900px] lg:h-[600px]"
              style={{ borderRadius: "8px" }}
            >
              <img
                src={organizationImage}
                alt="BCDP Organizational Structure"
                className="w-full h-auto lg:h-full object-contain"
                style={{ borderRadius: "8px" }}
              />
            </div>
            <div className="flex-1 flex flex-col justify-start">
              <Text
                variant="header"
                weight="normal"
                as="h2"
                className="text-gray-800 mb-4 sm:mb-6 text-xl sm:text-2xl lg:text-3xl text-center lg:text-left"
              >
                Organizational Structure
              </Text>
              <Text
                variant="body"
                weight="normal"
                as="p"
                className="text-sm sm:text-base lg:text-lg text-gray-600 leading-relaxed mb-4 text-left lg:text-justify"
              >
                The BCDP governance structure is led by the National Committee
                for Environment and Climate Change (ECECC), chaired by the
                Honorable Chief Adviser or Prime Minister. The Ministry of
                Environment, Forest and Climate Change (MOEFCC) serves as the
                Secretariat. This inter-ministerial mechanism adopts a "whole of
                government approach" to address institutional challenges and
                ensure coordinated policy and action on environmental and
                climate issues across all relevant sectors.
              </Text>
            </div>
          </div>
        </div>
      </div>

      <div className="px-4 py-8 sm:py-12 lg:py-5">
        <div className="max-w-container mx-auto">
          <Text
            variant="header"
            weight="normal"
            as="h2"
            className="text-gray-800 mb-6 sm:mb-8 lg:mb-10 text-xl sm:text-2xl lg:text-3xl text-center"
          >
            Key Officials
          </Text>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 lg:gap-8 justify-items-center">
            {officials.map((person, idx) => (
              <OfficialCard key={idx} {...person} />
            ))}
          </div>
        </div>
      </div>

      <div className="flex justify-center px-4 py-8 sm:py-12 lg:py-1">
        <div className="bg-light-green-bg max-w-container w-full px-4 sm:px-6 lg:px-8 py-6 sm:py-8 lg:py-12 rounded-lg">
          <div className="flex flex-col lg:flex-row gap-6 lg:gap-12 items-center">
            <div className="lg:w-[55%] flex flex-col justify-center">
              <Text
                variant="header"
                weight="normal"
                as="h2"
                className="text-gray-800 mb-4 sm:mb-6 text-xl sm:text-2xl lg:text-3xl text-center lg:text-left"
              >
                Why is the BCDP needed?
              </Text>
              <Text
                variant="secondary-header"
                weight="light"
                as="p"
                className="text-sm sm:text-base lg:text-lg text-gray-600 leading-relaxed text-left lg:text-justify"
              >
                Climate change is already a key threat to sustainable
                development. Bangladesh is one of the most vulnerable countries
                in the world with high levels of exposure to a range of
                climate-related hazards including floods, tropical cyclones,
                heat waves, and drought. The socioeconomic impacts of
                climate-related disasters are already being felt across the
                country. Such impacts are expected to grow by manifolds in the
                future due to changes in precipitation patterns with likely
                unprecedented extremes, increase in temperature, and sea level
                rise due to climate change. Further, rapid economic growth in
                the past decades has resulted in an increase in greenhouse gas
                emissions which is contributing to development challenges
                related to health from poor air quality and water quality to
                increasing trafc congestion. Estimates suggest that the efects
                of climate change could cause an average loss of about 1.3% of
                the gross domestic product per year until 2041, challenging the
                achievement of the country's long-term development goal.
                Government attaches highest priority to climate change
              </Text>
            </div>
            <div className="lg:w-[45%] flex-shrink-0 w-full max-w-md lg:max-w-none lg:h-[400px]">
              <img
                src="https://images.unsplash.com/photo-**********-cf412ec27db2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"
                alt="Climate development"
                className="w-full h-48 sm:h-64 lg:h-full object-cover rounded-lg shadow-lg"
              />
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-container mx-auto px-4 py-6 sm:py-8">
        <Text
          variant="header"
          weight="normal"
          as="h2"
          className="text-gray-800 mb-6 sm:mb-8 lg:mb-10 text-xl sm:text-2xl lg:text-3xl text-center"
        >
          Key update from us
        </Text>
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 sm:gap-6">
          {videos.map((video) => (
            <VideoCard key={video.id} {...video} />
          ))}
        </div>
      </div>
    </div>
  );
};

export default About;