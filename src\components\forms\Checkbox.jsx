import { useField } from 'formik';
import Text from '../Text';

const Checkbox = ({ 
  label, 
  className = '', 
  labelClassName = '',
  errorClassName = '',
  disabled = false,
  ...props 
}) => {
  const [field, meta] = useField({ ...props, type: 'checkbox' });
  const hasError = meta.touched && meta.error;

  return (
    <div className={`mb-4 ${className}`}>
      <label className={`flex items-center cursor-pointer ${disabled ? 'cursor-not-allowed opacity-60' : ''} ${labelClassName}`}>
        <input
          {...field}
          {...props}
          type="checkbox"
          disabled={disabled}
          className={`
            w-4 h-4 text-primary-600 bg-gray-100 border-gray-300 rounded focus:ring-primary-500 focus:ring-2
            ${hasError ? 'border-red-500' : ''}
            ${disabled ? 'cursor-not-allowed' : 'cursor-pointer'}
          `}
        />
        {label && (
          <Text 
            variant="body" 
            className={`ml-2 ${hasError ? 'text-red-500' : 'text-gray-700'}`}
          >
            {label}
          </Text>
        )}
      </label>
      
      {hasError && (
        <Text 
          variant="small" 
          className={`text-red-500 mt-1 ${errorClassName}`}
        >
          {meta.error}
        </Text>
      )}
    </div>
  );
};

export default Checkbox;
