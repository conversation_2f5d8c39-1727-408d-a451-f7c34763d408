import * as Yup from 'yup';

// Common validation patterns
export const validationPatterns = {
  // Phone number validation (supports various formats)
  phone: /^[+]?[\d\s\-\(\)]+$/,
  
  // Bangladesh phone number
  bdPhone: /^(\+88)?01[3-9]\d{8}$/,
  
  // Strong password (at least 8 chars, 1 uppercase, 1 lowercase, 1 number)
  strongPassword: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/,
  
  // URL validation
  url: /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/,
  
  // Alphanumeric only
  alphanumeric: /^[a-zA-Z0-9]+$/,
};

// Common validation schemas
export const commonValidations = {
  // Required string with min/max length
  requiredString: (min = 2, max = 100, fieldName = 'This field') => 
    Yup.string()
      .min(min, `${fieldName} must be at least ${min} characters`)
      .max(max, `${fieldName} must be less than ${max} characters`)
      .required(`${fieldName} is required`),

  // Email validation
  email: Yup.string()
    .email('Please enter a valid email address')
    .required('Email is required'),

  // Phone validation
  phone: Yup.string()
    .matches(validationPatterns.phone, 'Please enter a valid phone number'),

  // Bangladesh phone validation
  bdPhone: Yup.string()
    .matches(validationPatterns.bdPhone, 'Please enter a valid Bangladesh phone number'),

  // Password validation
  password: Yup.string()
    .min(8, 'Password must be at least 8 characters')
    .required('Password is required'),

  // Strong password validation
  strongPassword: Yup.string()
    .min(8, 'Password must be at least 8 characters')
    .matches(
      validationPatterns.strongPassword,
      'Password must contain at least one uppercase letter, one lowercase letter, and one number'
    )
    .required('Password is required'),

  // Confirm password validation
  confirmPassword: Yup.string()
    .oneOf([Yup.ref('password'), null], 'Passwords must match')
    .required('Please confirm your password'),

  // Age validation
  age: (min = 18, max = 100) => 
    Yup.number()
      .min(min, `Age must be at least ${min}`)
      .max(max, `Age must be less than ${max}`)
      .required('Age is required'),

  // Required select/dropdown
  requiredSelect: (fieldName = 'This field') => 
    Yup.object()
      .nullable()
      .required(`Please select ${fieldName.toLowerCase()}`),

  // Required multi-select
  requiredMultiSelect: (min = 1, fieldName = 'options') => 
    Yup.array()
      .min(min, `Please select at least ${min} ${fieldName}`)
      .required(`Please select ${fieldName}`),

  // File validation
  file: (maxSize = 5 * 1024 * 1024, allowedTypes = []) => {
    let schema = Yup.mixed().nullable();
    
    if (maxSize) {
      schema = schema.test(
        'fileSize',
        `File size must be less than ${Math.round(maxSize / (1024 * 1024))}MB`,
        (value) => !value || (value && value.size <= maxSize)
      );
    }
    
    if (allowedTypes.length > 0) {
      schema = schema.test(
        'fileType',
        `File type must be one of: ${allowedTypes.join(', ')}`,
        (value) => !value || (value && allowedTypes.includes(value.type))
      );
    }
    
    return schema;
  },

  // Required checkbox (for terms, etc.)
  requiredCheckbox: (message = 'You must accept this') => 
    Yup.boolean()
      .oneOf([true], message),

  // URL validation
  url: Yup.string()
    .matches(validationPatterns.url, 'Please enter a valid URL'),

  // Date validation
  date: (minDate, maxDate) => {
    let schema = Yup.date();
    
    if (minDate) {
      schema = schema.min(minDate, `Date must be after ${minDate.toLocaleDateString()}`);
    }
    
    if (maxDate) {
      schema = schema.max(maxDate, `Date must be before ${maxDate.toLocaleDateString()}`);
    }
    
    return schema;
  },
};

// Pre-built form schemas for common use cases
export const formSchemas = {
  // User registration form
  userRegistration: Yup.object({
    firstName: commonValidations.requiredString(2, 50, 'First name'),
    lastName: commonValidations.requiredString(2, 50, 'Last name'),
    email: commonValidations.email,
    phone: commonValidations.phone,
    password: commonValidations.strongPassword,
    confirmPassword: commonValidations.confirmPassword,
    dateOfBirth: commonValidations.date(new Date('1900-01-01'), new Date()),
    terms: commonValidations.requiredCheckbox('You must accept the terms and conditions'),
  }),

  // Contact form
  contact: Yup.object({
    name: commonValidations.requiredString(2, 100, 'Name'),
    email: commonValidations.email,
    phone: commonValidations.phone,
    subject: commonValidations.requiredString(5, 200, 'Subject'),
    message: commonValidations.requiredString(10, 1000, 'Message'),
  }),

  // Login form
  login: Yup.object({
    email: commonValidations.email,
    password: commonValidations.password,
    rememberMe: Yup.boolean(),
  }),

  // Profile update form
  profileUpdate: Yup.object({
    firstName: commonValidations.requiredString(2, 50, 'First name'),
    lastName: commonValidations.requiredString(2, 50, 'Last name'),
    phone: commonValidations.phone,
    bio: Yup.string().max(500, 'Bio must be less than 500 characters'),
    website: commonValidations.url,
    avatar: commonValidations.file(2 * 1024 * 1024, ['image/jpeg', 'image/png', 'image/gif']),
  }),
};

// Helper function to create custom validation schema
export const createValidationSchema = (fields) => {
  return Yup.object(fields);
};

// Helper function to validate single field
export const validateField = async (value, validation) => {
  try {
    await validation.validate(value);
    return null; // No error
  } catch (error) {
    return error.message;
  }
};
