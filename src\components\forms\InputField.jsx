import { useField } from "formik";
import { useState } from "react";
import { Icon } from "@iconify/react";
import Text from "../Text";

const InputField = ({
  label,
  type = "text",
  placeholder,
  className = "",
  labelClassName = "",
  errorClassName = "",
  required = false,
  disabled = false,
  showPasswordToggle = false,
  rightIcon = null,
  ...props
}) => {
  const [field, meta] = useField(props);
  const [showPassword, setShowPassword] = useState(false);
  const hasError = meta.touched && meta.error;

  const isPasswordField = type === "password";
  const inputType = isPasswordField && showPassword ? "text" : type;

  return (
    <div className={`mb-3 ${className}`}>
      {label && (
        <label htmlFor={props.name} className={`block mb-1 ${labelClassName}`}>
          <Text variant="body" weight="medium" className="text-gray-700">
            {label}
            {required && <span className="text-red-500 ml-1">*</span>}
          </Text>
        </label>
      )}

      <div className="relative">
        <input
          {...field}
          {...props}
          id={props.name}
          type={inputType}
          placeholder={placeholder}
          disabled={disabled}
          autoComplete={type === "password" ? "current-password" : "off"}
          className={`
            w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent
            ${
              hasError
                ? "border-red-500 bg-red-50"
                : "border-gray-300 bg-white hover:border-gray-400"
            }
            ${disabled ? "bg-gray-100 cursor-not-allowed" : ""}
            ${
              (showPasswordToggle && isPasswordField) || rightIcon
                ? "pr-10"
                : ""
            }
            transition-colors duration-200 relative z-10 text-gray-900
            placeholder-gray-300  // Lighter placeholder color
          `}
          style={{
            position: "relative",
            zIndex: 10,
            color: "#111827 !important",
            backgroundColor: "#ffffff !important",
            WebkitTextFillColor: "#111827",
          }}
        />

        {/* Right Icon or Password Toggle */}
        {((showPasswordToggle && isPasswordField) || rightIcon) && (
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center z-20">
            {showPasswordToggle && isPasswordField ? (
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="text-gray-400 hover:text-gray-600 focus:outline-none relative z-30"
                style={{ position: "relative", zIndex: 30 }}
              >
                <Icon
                  icon={
                    showPassword ? "mdi:eye-off-outline" : "mdi:eye-outline"
                  }
                  className="w-5 h-5"
                />
              </button>
            ) : rightIcon ? (
              <Icon icon={rightIcon} className="w-5 h-5 text-gray-400" />
            ) : null}
          </div>
        )}
      </div>

      {hasError && (
        <Text variant="small" className={`text-red-500 mt-1 ${errorClassName}`}>
          {meta.error}
        </Text>
      )}
    </div>
  );
};

export default InputField;
