import SummaryCard from "../../../components/SummaryCard";
import Text from "../../../components/Text";
import DevelopmentSection from "./DevelopmentSection";
import EmissionAndTrendCharts from "./EmissionAnTrendCharts";
import Piechart from "../../../components/Piechart";
import { Icon } from "@iconify/react";
import { useGetApiQuery } from "../../../store/api/commonApi";
import { Loader } from "../../../components";
import { useNavigate } from "react-router-dom";

const summaryData = [
  {
    title: "Total Projects",
    value: "1325",
    description: "Projects as of now",
    link: "/project-list",
  },
  {
    title: "On Going Projects",
    value: "743",
    description: "Projects as of now",
  },
  {
    title: "Total Commitments",
    value: "49,879",
    description: "Million USD",
  },
  {
    title: "Total Disbursements",
    value: "30,346",
    description: "Million USD",
  },
];

const workingGroupData = [
  {
    cardBgColor: "#F7FFFF",
    iconColor: "#3787FF",
    iconName: "akar-icons:home",
    header: "Climate Finance",
    type: "Domestic",
    link: "/working-group-one-dashboard",
  },
  {
    cardBgColor: "#F7EDFF",
    iconColor: "#8E3CCD",
    iconName: "fa6-solid:globe",
    header: "Renewable Energy",
    type: "Global",
    link: "working-group-two-dashboard",
  },
  {
    cardBgColor: "#FFF1F0",
    iconColor: "#FF847E",
    iconName: "ic:baseline-menu-book",
    header: "Sustainable Development",
    type: "Local",
    link: "working-group-three-dashboard",
  },
  {
    cardBgColor: "#FFFBEF",
    iconColor: "#F0BE27",
    iconName: "mdi:table-cog",
    header: "Green Economy",
    type: "International",
    link: "/working-group-four-dashboard",
  },
];

const WorkingGroupCard = ({
  cardBgColor,
  iconColor,
  iconName,
  header,
  type,
  link,
}) => {
  const navigate = useNavigate();
  return (
    <div
      onClick={() => navigate(`${link}`)}
      className={`
        h-[157px] w-[302px]
        shadow-[0px_2px_4px_0px_rgba(0,0,0,0.10)]
        border border-[#E8E8E8] border-solid rounded-lg p-5 relative
      `}
      style={{ backgroundColor: cardBgColor }}
    >
      <div className="absolute top-4 right-4">
        <div
          className={`
            p-4
            rounded-md
            shadow-[0px_2px_4px_0px_rgba(0,0,0,0.10)]
            flex items-center justify-center
          `}
        >
          <Icon
            className={`text-2xl`}
            style={{ color: iconColor || "#3787FF" }}
            icon={iconName || "akar-icons:home"}
          />
        </div>
      </div>
      <div className="h-full flex flex-col justify-center text-left pr-10">
        <Text variant="secondary-header" weight="normal" as="h3">
          {header}
        </Text>
        <Text
          variant="body"
          weight="normal"
          as="p"
          className="text-gray-600 mt-1"
        >
          ({type})
        </Text>
      </div>
    </div>
  );
};

const UserDashboard = () => {
  const { data, error, isLoading } = useGetApiQuery({
    url: "project-info/dashboard-data/",
  });
  const projectFinanceColors = ["#66FFB0", "#8BC2CA"];

  return isLoading ? (
    <div className="max-w-container mx-auto py-12 min-h-[80vh] flex items-center justify-center">
      <Loader />
    </div>
  ) : (
    <div className="max-w-container mx-auto py-12">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Summary Cards Section */}
        <div
          className="h-full flex flex-col p-4 rounded-lg border border-gray-200"
          style={{
            boxShadow: "0px 0px 12.75px 3.19px rgba(0, 0, 0, 0.08)",
          }}
        >
          <Text
            as="h2"
            variant="header"
            weight="bold"
            className="text-green-600 text-xl mb-4"
          >
            Summary
          </Text>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 flex-grow">
            {summaryData.map((item, idx) => (
              <SummaryCard
                key={idx}
                title={item.title}
                value={item.value}
                description={item.description}
                link={item.link}
              />
            ))}
          </div>
        </div>

        {/* 3D Pie Chart */}
        <Piechart
          header="Project Finance By"
          data={data?.charts?.project_finance_pie}
          colors={projectFinanceColors}
        />
      </div>
      <div className="my-16">
        <EmissionAndTrendCharts
          trendChartData={data?.charts?.trend_chart}
          barChartData={data?.charts?.emission_chart}
        />
      </div>
      <div>
        <Text
          variant="secondary-header"
          weight="normal"
          as="h3"
          className="text-primary-500 mb-3"
        >
          Working Groups
        </Text>
        <div className="grid grid-cols-4 gap-4">
          {workingGroupData?.map((item, index) => (
            <WorkingGroupCard
              key={index}
              cardBgColor={item.cardBgColor}
              iconColor={item.iconColor}
              iconName={item.iconName}
              header={item.header}
              type={item.type}
              link={item?.link}
            />
          ))}
        </div>
        <div className="max-w-container mx-auto">
          <DevelopmentSection
            pieData={data?.charts?.project_status_pie}
            tableData={data?.tables?.govt_projects}
          />
        </div>
        <div className="max-w-container mx-auto">
          <DevelopmentSection
            pieData={data?.charts?.project_status_pie}
            tableData={data?.tables?.govt_projects}
          />
        </div>
      </div>
    </div>
  );
};

export default UserDashboard;
