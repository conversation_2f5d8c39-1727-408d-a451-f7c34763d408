import { useField } from 'formik';
import Text from '../Text';

const RadioGroup = ({ 
  label, 
  options = [],
  className = '', 
  labelClassName = '',
  errorClassName = '',
  optionClassName = '',
  disabled = false,
  inline = false,
  required = false,
  ...props 
}) => {
  const [field, meta] = useField(props);
  const hasError = meta.touched && meta.error;

  return (
    <div className={`mb-4 ${className}`}>
      {label && (
        <div className={`mb-3 ${labelClassName}`}>
          <Text variant="body" weight="medium" className="text-gray-700">
            {label}
            {required && <span className="text-red-500 ml-1">*</span>}
          </Text>
        </div>
      )}
      
      <div className={`${inline ? 'flex flex-wrap gap-4' : 'space-y-2'}`}>
        {options.map((option) => (
          <label 
            key={option.value} 
            className={`
              flex items-center cursor-pointer 
              ${disabled ? 'cursor-not-allowed opacity-60' : ''} 
              ${optionClassName}
            `}
          >
            <input
              {...field}
              type="radio"
              value={option.value}
              disabled={disabled}
              className={`
                w-4 h-4 text-primary-600 bg-gray-100 border-gray-300 focus:ring-primary-500 focus:ring-2
                ${hasError ? 'border-red-500' : ''}
                ${disabled ? 'cursor-not-allowed' : 'cursor-pointer'}
              `}
            />
            <Text 
              variant="body" 
              className={`ml-2 ${hasError ? 'text-red-500' : 'text-gray-700'}`}
            >
              {option.label}
            </Text>
          </label>
        ))}
      </div>
      
      {hasError && (
        <Text 
          variant="small" 
          className={`text-red-500 mt-1 ${errorClassName}`}
        >
          {meta.error}
        </Text>
      )}
    </div>
  );
};

export default RadioGroup;
